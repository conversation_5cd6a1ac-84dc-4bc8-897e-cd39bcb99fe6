///<summary>
///角色的数值属性部分，比如最大hp、攻击力等等都在这里
///这个建一个结构是因为并非只有角色有这些属性，包括装备、buff、aoe、damageInfo等都会用上
///</summary>
class ChaProperty {
    ///<summary>
    ///最大生命，基本都得有，哪怕角色只有1，装备可以是0
    ///</summary>
    hp: number;

    ///<summary>
    ///攻击力
    ///</summary>
    attack: number;

    ///<summary>
    ///移动速度，他不是米/秒作为单位的，而是一个可以培养的数值。
    ///具体转化为米/秒，是需要一个规则的，所以是策划脚本 int SpeedToMoveSpeed(int speed)来返回
    ///</summary>
    moveSpeed: number;

    ///<summary>
    ///行动速度，和移动速度不同，他是增加角色行动速度，也就是变化timeline和动画播放的scale的，比如wow里面开嗜血就是加行动速度
    ///具体多少也不是一个0.2f（我这个游戏中规则设定的最快为正常速度的20%，你的游戏你自己定）到5.0f（我这个游戏设定了最慢是正常速度20%），和移动速度一样需要脚本接口返回策划公式
    ///</summary>
    actionSpeed: number;

    ///<summary>
    ///弹仓，其实相当于mp了，只是我是射击游戏所以题材需要换皮。
    ///玩家层面理解，跟普通mp上限的区别是角色这个值上限一般都是0，它来自于装备。
    ///</summary>
    ammo: number;

    ///<summary>
    ///体型圆形半径，用于移动碰撞的，单位：米
    ///这个属性因人而异，但是其实在玩法中几乎不可能经营它，只有buff可能会改变一下，所以直接用游戏中用的数据就行了，不需要转化了
    ///</summary>
    bodyRadius: number;

    ///<summary>
    ///挨打圆形半径，同体型圆形，只是用途不同，用在判断子弹是否命中的时候
    ///</summary>
    hitRadius: number;

    ///<summary>
    ///角色移动类型
    ///</summary>
    moveType: MoveType;

    constructor(
        moveSpeed: number, 
        hp: number = 0, 
        ammo: number = 0, 
        attack: number = 0, 
        actionSpeed: number = 100, 
        bodyRadius: number = 0.25, 
        hitRadius: number = 0.25, 
        moveType: MoveType = MoveType.ground
    ) {
        this.moveSpeed = moveSpeed;
        this.hp = hp;
        this.ammo = ammo;
        this.attack = attack;
        this.actionSpeed = actionSpeed;
        this.bodyRadius = bodyRadius;
        this.hitRadius = hitRadius;
        this.moveType = moveType;
    }

    static zero: ChaProperty = new ChaProperty(0, 0, 0, 0, 0, 0, 0, 0);

    ///<summary>
    ///将所有值清0
    ///<param name="moveType">移动类型设置为</param>
    ///</summary>
    Zero(moveType: MoveType = MoveType.ground): void {
        this.hp = 0;
        this.moveSpeed = 0;
        this.ammo = 0;
        this.attack = 0;
        this.actionSpeed = 0;
        this.bodyRadius = 0;
        this.hitRadius = 0;
        this.moveType = moveType;
    }

    //定义加法和乘法的用法，其实这个应该走脚本函数返回，抛给脚本函数多个ChaProperty，由脚本函数运作他们的运算关系，并返回结果
    static add(a: ChaProperty, b: ChaProperty): ChaProperty {
        return new ChaProperty(
            a.moveSpeed + b.moveSpeed,
            a.hp + b.hp,
            a.ammo + b.ammo,
            a.attack + b.attack,
            a.actionSpeed + b.actionSpeed,
            a.bodyRadius + b.bodyRadius,
            a.hitRadius + b.hitRadius,
            a.moveType === MoveType.fly || b.moveType === MoveType.fly ? MoveType.fly : MoveType.ground
        );
    }

    static multiply(a: ChaProperty, b: ChaProperty): ChaProperty {
        return new ChaProperty(
            Math.round(a.moveSpeed * (1.0000 + Math.max(b.moveSpeed, -0.9999))),
            Math.round(a.hp * (1.0000 + Math.max(b.hp, -0.9999))),
            Math.round(a.ammo * (1.0000 + Math.max(b.ammo, -0.9999))),
            Math.round(a.attack * (1.0000 + Math.max(b.attack, -0.9999))),
            Math.round(a.actionSpeed * (1.0000 + Math.max(b.actionSpeed, -0.9999))),
            a.bodyRadius * (1.0000 + Math.max(b.bodyRadius, -0.9999)),
            a.hitRadius * (1.0000 + Math.max(b.hitRadius, -0.9999)),
            a.moveType === MoveType.fly || b.moveType === MoveType.fly ? MoveType.fly : MoveType.ground
        );
    }

    static multiplyByFloat(a: ChaProperty, b: number): ChaProperty {
        return new ChaProperty(
            Math.round(a.moveSpeed * b),
            Math.round(a.hp * b),
            Math.round(a.ammo * b),
            Math.round(a.attack * b),
            Math.round(a.actionSpeed * b),
            a.bodyRadius * b,
            a.hitRadius * b,
            a.moveType
        );
    }
}

///<summary>
///角色的资源类属性，比如hp，mp等都属于这个
///</summary>
class ChaResource {
    ///<summary>
    ///当前生命
    ///</summary>
    hp: number;

    ///<summary>
    ///当前弹药量，在这游戏里就相当于mp了
    ///</summary>
    ammo: number;

    ///<summary>
    ///当前耐力，耐力是一个百分比消耗，实时恢复的概念，所以上限按规则就是100了，这里是现有多少
    ///</summary>
    stamina: number;

    constructor(hp: number, ammo: number = 0, stamina: number = 0) {
        this.hp = hp;
        this.ammo = ammo;
        this.stamina = stamina;
    }

    ///<summary>
    ///是否足够
    ///</summary>
    Enough(requirement: ChaResource): boolean {
        return (
            this.hp >= requirement.hp &&
            this.ammo >= requirement.ammo &&
            this.stamina >= requirement.stamina
        );
    }

    static add(a: ChaResource, b: ChaResource): ChaResource {
        return new ChaResource(
            a.hp + b.hp,
            a.ammo + b.ammo,
            a.stamina + b.stamina
        );
    }

    static multiplyByFloat(a: ChaResource, b: number): ChaResource {
        return new ChaResource(
            Math.floor(a.hp * b),
            Math.floor(a.ammo * b),
            Math.floor(a.stamina * b)
        );
    }

    static subtract(a: ChaResource, b: ChaResource): ChaResource {
        return ChaResource.add(a, ChaResource.multiplyByFloat(b, -1));
    }
}

enum MoveType {
    ground,
    fly
}