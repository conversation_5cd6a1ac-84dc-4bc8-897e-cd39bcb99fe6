///<summary>
///AoE的状态控制器
///</summary>
export class AoeState extends MonoBehaviour {
    ///<summary>
    ///要释放的aoe
    ///</summary>
    public model: AoeModel;

    ///<summary>
    ///是否被视作刚创建
    ///</summary>
    public justCreated: boolean = true;

    ///<summary>
    ///aoe的半径，单位：米
    ///目前这游戏的设计中，aoe只有圆形，所以只有一个半径，也不存在角度一说，如果需要可以扩展
    ///</summary>
    public radius: number;

    ///<summary>
    ///aoe的施法者
    ///</summary>
    public caster: GameObject;

    ///<summary>
    ///aoe存在的时间，单位：秒
    ///</summary>
    public duration: number;

    ///<summary>
    ///aoe已经存在过的时间，单位：秒
    ///</summary>
    public timeElapsed: number = 0;

    ///<summary>
    ///aoe移动轨迹函数
    ///</summary>
    public tween: AoeTween;

    ///<summary>
    ///aoe的轨迹运行了多少时间了，单位：秒
    ///<summary>
    public tweenRunnedTime: number = 0;

    ///<summary>
    ///创建时角色的属性
    ///</summary>
    public propWhileCreate: ChaProperty;

    ///<summary>
    ///aoe的传入参数，比如可以吸收次数之类的
    ///</summary>
    public param: { [key: string]: any } = {};

    ///<summary>
    ///现在aoe范围内的所有角色的gameobject
    ///</summary>
    public characterInRange: GameObject[] = [];

    ///<summary>
    ///现在aoe范围内的所有子弹的gameobject
    ///</summary>
    public bulletInRange: GameObject[] = [];

    ///<summary>
    ///Tween函数的参数
    ///</summary>
    public tweenParam: any[];

    ///<summary>
    ///移动信息
    ///</summary>
    public get velocity(): Vector3 {
        return this._velo;
    }
    private _velo: Vector3 = new Vector3();

    private unitMove: UnitMove;
    private unitRotate: UnitRotate;
    private viewContainer: GameObject;

    private Start(): void {
        // this.unitMove = this.gameObject.GetComponent<UnitMove>();
        // this.unitRotate = this.gameObject.GetComponent<UnitRotate>();
        this.synchronizedUnits();
    }

    ///<summary>
    ///设置移动和旋转的信息，用于执行
    ///</summary>
    public SetMoveAndRotate(aoeMoveInfo: AoeMoveInfo): void {
        if (aoeMoveInfo != null) {
            if (this.unitMove) {
                this.unitMove.moveType = aoeMoveInfo.moveType;
                this.unitMove.bodyRadius = this.radius;
                this._velo = Vector3.divideByFloat(aoeMoveInfo.velocity, Time.fixedDeltaTime);
                this.unitMove.MoveBy(this._velo);
            }
            if (this.unitRotate) {
                this.unitRotate.RotateTo(aoeMoveInfo.rotateToDegree);
            }
        }
    }

    public HitObstacle(): boolean {
        return this.unitMove == null ? false : this.unitMove.hitObstacle;
    }

    private synchronizedUnits(): void {
        if (!this.unitMove) this.unitMove = this.gameObject.GetComponent<UnitMove>();
        if (!this.unitRotate) this.unitRotate = this.gameObject.GetComponent<UnitRotate>();
        if (!this.viewContainer) this.viewContainer = this.gameObject.GetComponentInChildren<ViewContainer>().gameObject;
        this.unitMove.bodyRadius = this.radius;
        this.unitMove.smoothMove = !this.model.removeOnObstacle;
    }

    public InitByAoeLauncher(aoe: AoeLauncher): void {
        this.model = aoe.model;
        this.radius = aoe.radius;
        this.duration = aoe.duration;
        this.timeElapsed = 0;
        this.tween = aoe.tween;
        this.tweenParam = aoe.tweenParam;
        this.tweenRunnedTime = 0;
        this.param = {};
        for (const key in aoe.param) {
            this.param[key] = aoe.param[key];
        }//aoe.param;
        this.caster = aoe.caster;
        this.propWhileCreate = aoe.caster ? aoe.caster.GetComponent<ChaState>().property : ChaProperty.zero;
        
        this.transform.position = aoe.position;
        this.transform.eulerAngles.Set(0, aoe.degree, 0);

        this.synchronizedUnits();

        //把视觉特效给aoe
        if (aoe.model.prefab != "") {
            const aoeEffect = Instantiate<GameObject>(
                Resources.Load<GameObject>("Prefabs/" + aoe.model.prefab),
                new Vector3(),
                Quaternion.identity,
                this.viewContainer.transform
            );
            
            aoeEffect.transform.localPosition = new Vector3(0, this.gameObject.transform.position.y, 0);
            aoeEffect.transform.localRotation = Quaternion.identity;
        }
        this.gameObject.transform.position = new Vector3(
            this.gameObject.transform.position.x,
            0,
            this.gameObject.transform.position.z
        );
    }

    ///<summary>
    ///改变aoe视觉的尺寸
    ///</summary>
    public SetViewScale(scaleX: number = 1, scaleY: number = 1, scaleZ: number = 1): void {
        this.synchronizedUnits();
        this.viewContainer.transform.localScale.Set(scaleX, scaleY, scaleZ);
    }

    ///<summary>
    ///改变图形的y高度
    ///</summary>
    public ModViewY(toY: number): void {
        this.viewContainer.transform.position = new Vector3(
            this.viewContainer.transform.position.x,
            toY,
            this.viewContainer.transform.position.z
        );
    }
}
