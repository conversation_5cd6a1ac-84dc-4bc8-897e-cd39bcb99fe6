///<summary>
///动画播放的管理器，是典型的ARPG之类即时回合制使用的，用来管理当前应该播放那个动画
///不仅仅是角色，包括aoe、bullet等，只要需要管理播放什么动画（带有animator的gameobject）就应该用这个
///</summary>
export class UnitAnim extends MonoBehaviour {
    private animator: Animator;

    ///<summary>
    ///播放的倍速，作用于每个信息的duration减少速度
    ///</summary>
    public timeScale: number = 1;

    ///<summary>
    ///动画的逻辑信息
    ///key其实就是要播放的动画的key，比如"attack"等。
    ///value则是一个animInfo，取其RandomKey()的值就可以得到要播放的动画在animator中的名称（play()的参数）
    ///</summary>
    public animInfo: { [key: string]: AnimInfo };

    //当前正在播放的动画的权重，只有权重>=这个值才会切换动画
    private playingAnim: AnimInfo = null;

    //当前权重持续时间（单位秒），归0后，currentPriority归0
    private priorityDuration: number = 0;

    private get currentAnimPriority(): number {
        return this.playingAnim == null ? 0 : 
            (this.priorityDuration <= 0 ? 0 : this.playingAnim.priority);
    }

    ///<summary>
    ///正在播放的动画
    ///</summary>

    Start(): void {
        this.animator = this.gameObject.GetComponent<Animator>();
    }

    FixedUpdate(): void {
        if (!this.animator) this.animator = this.gameObject.GetComponentInChildren<Animator>();   //尝试从子级GameObject找到一个Animator

        if (!this.animator || this.animInfo == null || Object.keys(this.animInfo).length <= 0) return;   

        if (this.priorityDuration > 0) this.priorityDuration -= Time.fixedDeltaTime * this.timeScale;
    }

    ///<summary>
    ///申请播放某个动画，不是你申请就鸟你了，要看有什么正在播放的
    ///<param name="animName">动画的名称，对应animInfo的key</param>
    ///</summary>
    public Play(animName: string): void {
        if (!(animName in this.animInfo) || this.animator == null) return;
        if (this.playingAnim != null && this.playingAnim.key == animName) return;  //已经在播放了
        const toPlay = this.animInfo[animName];
        if (this.currentAnimPriority > toPlay.priority) return;   //优先级不够不放
        const playOne = toPlay.RandomKey();
        this.animator.Play(playOne.animName);
        this.playingAnim = toPlay;
        this.priorityDuration = playOne.duration;
    }

    ///<summary>
    ///设置Animator为对象
    ///<param name="animator">要被这个unitAnim所管理的animator</param>
    ///</summary>
    public SetAnimator(animator: Animator): void {
        this.animator = animator;
    }
}
