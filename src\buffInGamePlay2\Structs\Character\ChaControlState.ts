///<summary>
///角色的可操作状态，这个是根据游戏玩法来细节设计的，目前就用这个demo需要的
///</summary>
export class ChaControlState {
    ///<summary>
    ///是否可以移动坐标
    ///</summary>
    public canMove: boolean;

    ///<summary>
    ///是否可以转身
    ///</summary>
    public canRotate: boolean;

    ///<summary>
    ///是否可以使用技能，这里的是"使用技能"特指整个技能流程是否可以开启
    ///如果是类似中了沉默，则应该走buff的onCast，尤其是类似wow里面沉默了不能施法但是还能放致死打击（部分技能被分类为法术，会被沉默，而不是法术的不会）
    ///</summary>
    public canUseSkill: boolean;

    constructor(canMove: boolean = true, canRotate: boolean = true, canUseSkill: boolean = true) {
        this.canMove = canMove;
        this.canRotate = canRotate;
        this.canUseSkill = canUseSkill;
    }

    public Origin(): void {
        this.canMove = true;
        this.canRotate = true;
        this.canUseSkill = true;
    }

    public static origin = new ChaControlState(true, true, true);

    ///<summary>
    ///昏迷效果
    ///</summary>
    public static stun = new ChaControlState(false, false, false);

    public static add(cs1: ChaControlState, cs2: ChaControlState): ChaControlState {
        return new ChaControlState(
            cs1.canMove && cs2.canMove,
            cs1.canRotate && cs2.canRotate,
            cs1.canUseSkill && cs2.canUseSkill
        );
    }
}
