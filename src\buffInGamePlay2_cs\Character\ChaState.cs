///<summary>
///角色的"状态"，用来管理当前应该怎么移动、应该怎么旋转、应该怎么播放动画的。
///是一个角色的总的"调控中心"。
///</summary>
export class ChaState extends MonoBehaviour {
    ///<summary>
    //角色最终的可操作性状态
    ///</summary>
    private _controlState: ChaControlState = new ChaControlState(true, true, true);

    ///<summary>
    ///GameTimeline专享的ChaControlState
    ///</summary>
    public timelineControlState: ChaControlState = new ChaControlState(true,true,true);

    public get controlState(): ChaControlState {
        return ChaControlState.add(this._controlState, this.timelineControlState);
    }

    ///<summary>
    ///角色的无敌状态持续时间，如果在无敌状态中，子弹不会碰撞，DamageInfo处理无效化
    ///单位：秒
    ///</summary>
    public get immuneTime(): number {
        return this._immuneTime;
    }
    public set immuneTime(value: number) {
        this._immuneTime = Math.max(this._immuneTime, value);
    }
    private _immuneTime: number = 0.00;

    ///<summary>
    ///角色是否处于一种蓄力的状态
    ///</summary>
    public charging: boolean = false;

    ///<summary>
    ///角色主动期望的移动方向
    ///</summary>
    public get moveDegree(): number {
        return this._wishToMoveDegree;
    }
    private _wishToMoveDegree: number = 0.00;

    ///<summary>
    ///角色主动期望的面向
    ///</summary>
    public get faceDegree(): number {
        return this._wishToFaceDegree;
    }
    private _wishToFaceDegree: number = 0.00;

    ///<summary>
    ///角色是否已经死了，这不由我这个系统判断，其他系统应该告诉我
    ///</summary>
    public dead: boolean = false;

    //来自操作或者ai的移动请求信息
    private moveOrder: Vector3 = new Vector3();

    //来自强制发生的位移信息，通常是技能效果等导致的，比如翻滚、被推开等
    private forceMove: MovePreorder[] = [];

    //收到的来自各方的播放动画的请求
    private animOrder: string[] = [];

    //来自操作或者ai的旋转角度请求
    private rotateToOrder: number;

    //来自强制执行的旋转角度
    private forceRotate: number[] = [];

    ///<summary>
    ///角色现有的资源，比如hp之类的
    ///</summary>
    public resource: ChaResource = new ChaResource(1);

    //[Tooltip("角色所处阵营，阵营不同就会对打")]
    ///<summary>
    ///角色所处阵营，阵营不同就会对打
    ///</summary>
    public side: number = 0;

    ///<summary>
    ///根据tags可以判断出这是什么样的人
    ///</summary>
    public tags: string[] = [];

    ///<summary>
    ///角色当前的属性
    ///</summary>
    public get property(): ChaProperty {
        return this._prop;
    }
    private _prop: ChaProperty = ChaProperty.zero;

    ///<summary>
    ///角色移动力，单位：米/秒
    ///</summary>
    public get moveSpeed(): number {
        //这个公式也可以通过给策划脚本接口获得，这里就写代码里了，不走策划脚本了
        //设定，值=0.2+5.6*x/(x+100)，初始速度是100，移动力3米/秒，最小值0.2米/秒。
        return this._prop.moveSpeed * 5.600 / (this._prop.moveSpeed + 100.000) + 0.200;
    }

    ///<summary>
    ///角色行动速度，是一个timescale，最小0.1，初始行动速度值也是100。
    ///</summary>
    public get actionSpeed(): number {
        return this._prop.actionSpeed * 4.90 / (this._prop.actionSpeed + 390.00) + 0.100;
    }

    ///<summary>
    ///角色的基础属性，也就是每个角色"裸体"且不带任何buff的"纯粹的属性"
    ///先写死，正式的应该读表
    ///</summary>
    public baseProp: ChaProperty = new ChaProperty(
        100, 100, 0, 20, 100
    );

    ///<summary>
    ///角色来自buff的属性
    ///这个数组并不是说每个buff可以占用一条数据，而是分类总和
    ///在这个游戏里buff带来的属性总共有2类，plus和times，用策划设计的公式就是plus的属性加完之后乘以times的属性
    ///所以数组长度其实只有2：[0]buffPlus, [1]buffTimes
    ///</summary>
    public buffProp: ChaProperty[] = [ChaProperty.zero, ChaProperty.zero];

    ///<summary>
    ///来自装备的属性
    ///</summary>
    public equipmentProp: ChaProperty = ChaProperty.zero;

    ///<summary>
    ///角色的技能
    ///</summary>
    public skills: SkillObj[] = [];

    ///<summary>
    ///角色身上的buff
    ///</summary>
    public buffs: BuffObj[] = [];


    private unitMove: UnitMove;
    private unitAnim: UnitAnim;
    private unitRotate: UnitRotate;
    private animator: Animator;
    private bindPoints: UnitBindManager;
    private viewContainer: GameObject;

    Start(): void {
        this.rotateToOrder = this.transform.rotation.eulerAngles.y;

        this.synchronizedUnits();

        this.AttrRecheck();
    }

    FixedUpdate(): void {
        const timePassed = Time.fixedDeltaTime;
        if (this.dead == false) {
            //如果角色没死，做这些事情：

            //无敌时间减少
            if (this._immuneTime > 0) this._immuneTime -= timePassed;

            //技能冷却时间
            for (let i = 0; i < this.skills.length; i++) {
                if (this.skills[i].cooldown > 0) {
                    this.skills[i].cooldown -= timePassed;
                }
            }

            //对身上的buff进行管理
            const toRemove: BuffObj[] = [];
            for (let i = 0; i < this.buffs.length; i++) {
                if (this.buffs[i].permanent == false) this.buffs[i].duration -= timePassed;
                this.buffs[i].timeElapsed += timePassed;

                if (this.buffs[i].model.tickTime > 0 && this.buffs[i].model.onTick != null) {
                    //float取模不精准，所以用x1000后的整数来
                    if (Math.round(this.buffs[i].timeElapsed * 1000) % Math.round(this.buffs[i].model.tickTime * 1000) == 0) {
                        this.buffs[i].model.onTick(this.buffs[i]);
                        this.buffs[i].ticked += 1;
                    }
                }

                //只要duration <= 0，不管是否是permanent都移除掉
                if (this.buffs[i].duration <= 0 || this.buffs[i].stack <= 0) {
                    if (this.buffs[i].model.onRemoved != null) {
                        this.buffs[i].model.onRemoved(this.buffs[i]);
                    }
                    toRemove.push(this.buffs[i]);
                }
            }
            if (toRemove.length > 0) {
                for (let i = 0; i < toRemove.length; i++) {
                    const index = this.buffs.indexOf(toRemove[i]);
                    if (index > -1) this.buffs.splice(index, 1);
                }
                this.AttrRecheck();
            }

            //给各个系统发消息
            const wishToMove = !Vector3.equals(this.moveOrder, Vector3.zero);
            if (wishToMove == true)
            this._wishToMoveDegree = Math.atan2(this.moveOrder.x, this.moveOrder.z) * 180 / Math.PI;

            const curCS = this.controlState;// _controlState + timelineControlState;

            //首先是合并移动信息，发送给UnitMove
            const tryRun = curCS.canMove == true && !Vector3.equals(this.moveOrder, Vector3.zero);
            let tryMoveDegree = Math.atan2(this.moveOrder.x, this.moveOrder.z) * 180 / Math.PI;
            if (tryMoveDegree > 180) tryMoveDegree -= 360;
            if (this.unitMove) {
                if (curCS.canMove == false) this.moveOrder = Vector3.zero;
                let fmIndex = 0;
                while (fmIndex < this.forceMove.length) {
                    this.moveOrder = Vector3.add(this.moveOrder, this.forceMove[fmIndex].VeloInTime(timePassed));
                    if (this.forceMove[fmIndex].duration <= 0) {
                        this.forceMove.splice(fmIndex, 1);
                    } else {
                        fmIndex++;
                    }
                }
                this.unitMove.MoveBy(this.moveOrder);
                this.moveOrder = Vector3.zero;
                //forceMove.Clear();
            }

            this._wishToFaceDegree = this.rotateToOrder;
            if (wishToMove == false) this._wishToMoveDegree = this._wishToFaceDegree;
            //然后是旋转信息
            if (this.unitRotate) {
                if (curCS.canRotate == false) this.rotateToOrder = this.transform.rotation.eulerAngles.y;
                for (let i = 0; i < this.forceRotate.length; i++) {
                    //这里全是增量，而不是设定为，所以可以直接加
                    this.rotateToOrder += this.forceRotate[i];
                }
                this.unitRotate.RotateTo(this.rotateToOrder);
                this.forceRotate = [];
            }
            //再是动画处理
            if (this.unitAnim) {
                this.unitAnim.timeScale = this.actionSpeed;
                //先计算默认（规则下）的动画，并且添加到动画组
                if (tryRun == false) {
                    this.animOrder.push("Stand");    //如果没有要求移动，就用站立
                } else {
                    const tt = Utils.GetTailStringByDegree(this.transform.rotation.eulerAngles.y, tryMoveDegree);
                    this.animOrder.push("Move" + tt);
                }
                //送给动画系统处理
                for (let i = 0; i < this.animOrder.length; i++) {
                    this.unitAnim.Play(this.animOrder[i]);
                }
                this.animOrder = [];
            }
            if (this.animator) {
                this.animator.speed = this.actionSpeed;
            }
        } else {
            this._wishToFaceDegree = this.transform.rotation.eulerAngles.y * 180.00 / Math.PI;
            this._wishToMoveDegree = this._wishToFaceDegree;
        }
    }

    private synchronizedUnits(): void {
        if (!this.unitMove) this.unitMove = this.gameObject.GetComponent<UnitMove>();
        if (!this.unitAnim) this.unitAnim = this.gameObject.GetComponent<UnitAnim>();
        if (!this.unitRotate) this.unitRotate = this.gameObject.GetComponent<UnitRotate>();
        if (!this.animator) this.animator = this.gameObject.GetComponent<Animator>();
        if (!this.bindPoints) this.bindPoints = this.gameObject.GetComponent<UnitBindManager>();
        if (!this.viewContainer) this.viewContainer = this.gameObject.GetComponentInChildren<ViewContainer>().gameObject;
    }

    ///<summary>
    ///命令移动
    ///<param name="move">移动力</param>
    ///</summary>
    public OrderMove(move: Vector3): void {
        this.moveOrder.x = move.x;
        this.moveOrder.z = move.z;
    }

    ///<summary>
    ///强制移动
    ///<param name="moveInfo">移动信息</param>
    ///</summary>
    public AddForceMove(move: MovePreorder): void {
        this.forceMove.push(move);
    }

    ///<summary>
    ///命令旋转到多少度
    ///<param name="degree">旋转目标</param>
    ///</summary>
    public OrderRotateTo(degree: number): void {
        this.rotateToOrder = degree;
    }

    ///<summary>
    ///强制旋转的力量
    ///<param name="degree">偏移角度</param>
    ///</summary>
    public AddForceRotate(degree: number): void {
        this.forceRotate.push(degree);
    }

    ///<summary>
    ///添加角色要做的动作请求
    ///<param name="animName">要做的动作</param>
    ///</summary>
    public Play(animName: string): void {
        this.animOrder.push(animName);
    }

    ///<summary>
    ///杀死这个角色
    ///</summary>
    public Kill(): void {
        this.dead = true;
        if (this.unitAnim) {
            this.unitAnim.Play("Dead");
        }
        //如果不是主角，尸体就会消失
        if (this.gameObject != SceneVariants.MainActor())
            this.gameObject.AddComponent<UnitRemover>().duration = 5.0;
    }

    ///<summary>
    ///重新计算所有属性，并且获得一个最终属性
    ////其实这个应该走脚本函数返回，抛给脚本函数多个ChaProperty，由脚本函数运作他们的运算关系，并返回结果
    ///</summary>
    private AttrRecheck(): void {
        this._controlState.Origin();
        this._prop.Zero();

        for (let i = 0; i < this.buffProp.length; i++) this.buffProp[i].Zero();
        for (let i = 0; i < this.buffs.length; i++) {
            for (let j = 0; j < Math.min(this.buffProp.length, this.buffs[i].model.propMod.length); j++) {
                this.buffProp[j] = ChaProperty.add(this.buffProp[j], ChaProperty.multiplyByFloat(this.buffs[i].model.propMod[j], this.buffs[i].stack));
            }
            this._controlState = ChaControlState.add(this._controlState, this.buffs[i].model.stateMod);
        }

        this._prop = ChaProperty.multiply(ChaProperty.add(ChaProperty.add(this.baseProp, this.equipmentProp), this.buffProp[0]), this.buffProp[1]);

        if (this.unitMove) {
            this.unitMove.bodyRadius = this._prop.bodyRadius;
        }
    }

    ///<summary>
    ///增加角色的血量等资源，直接改变数字的，属于最后一步操作了
    ///<param name="value">要改变的量，负数为减少</param>
    ///</summary>
    public ModResource(value: ChaResource): void {
        this.resource = ChaResource.add(this.resource, value);
        this.resource.hp = Math.max(0, Math.min(this.resource.hp, this._prop.hp));
        this.resource.ammo = Math.max(0, Math.min(this.resource.ammo, this._prop.ammo));
        this.resource.stamina = Math.max(0, Math.min(this.resource.stamina, 100));
        if (this.resource.hp <= 0) {
            this.Kill();
        }
    }


    ///<summary>
    ///在角色身上放一个特效，其实是挂在一个gameObject而已
    ///<param name="bindPointKey">绑点名称，角色有Muzzle/Head/Body这3个，需要再加</param>
    ///<param name="effect">要播放的特效文件名，统一走Prefabs/下拿</param>
    ///<param name="effectKey">这个特效的key，要删除的时候就有用了</param>
    ///<param name="effect">要播放的特效</param>
    ///</summary>
    public PlaySightEffect(bindPointKey: string, effect: string, effectKey: string = "", loop: boolean = false): void {
        this.bindPoints.AddBindGameObject(bindPointKey, "Prefabs/" + effect, effectKey, loop);
    }

    ///<summary>
    ///删除角色身上的一个特效
    ///<param name="bindPointKey">绑点名称，角色有Muzzle/Head/Body这3个，需要再加</param>
    ///<param name="effectKey">这个特效的key，要删除的时候就有用了</param>
    ///</summary>
    public StopSightEffect(bindPointKey: string, effectKey: string): void {
        this.bindPoints.RemoveBindGameObject(bindPointKey, effectKey);
    }

    ///<summary>
    ///判断这个角色是否会被这个damageInfo所杀
    ///<param name="dInfo">要判断的damageInfo</param>
    ///<return>如果是true代表角色可能会被这次伤害所杀</return>
    ///</summary>
    public CanBeKilledByDamageInfo(damageInfo: DamageInfo): boolean {
          if (this.immuneTime > 0 || damageInfo.isHeal() == true) return false;
        const dValue = damageInfo.DamageValue(false);
        return dValue >= this.resource.hp;
    }

    ///<summary>
    ///为角色添加buff，当然，删除也是走这个的
    ///</summary>
    public AddBuff(buff: AddBuffInfo): void {
        const bCaster: GameObject[] = [];
        if (buff.caster) bCaster.push(buff.caster);
        const hasOnes = this.GetBuffById(buff.buffModel.id, bCaster);
        let modStack = Math.min(buff.addStack, buff.buffModel.maxStack);
        let toRemove = false;
        let toAddBuff: BuffObj = null;
        if (hasOnes.length > 0) {
            //已经存在
            hasOnes[0].buffParam = {};
            if (buff.buffParam != null) {
                for (const key in buff.buffParam) {
                    hasOnes[0].buffParam[key] = buff.buffParam[key];
                }
            }

            hasOnes[0].duration = (buff.durationSetTo == true) ? buff.duration : (buff.duration + hasOnes[0].duration);
            const afterAdd = hasOnes[0].stack + modStack;
            modStack = afterAdd >= hasOnes[0].model.maxStack ?
                (hasOnes[0].model.maxStack - hasOnes[0].stack) :
                (afterAdd <= 0 ? (0 - hasOnes[0].stack) : modStack);
            hasOnes[0].stack += modStack;
            hasOnes[0].permanent = buff.permanent;
            toAddBuff = hasOnes[0];
            toRemove = hasOnes[0].stack <= 0;
        } else {
            //新建
            toAddBuff = new BuffObj(
                buff.buffModel,
                buff.caster,
                this.gameObject,
                buff.duration,
                buff.addStack,
                buff.permanent,
                buff.buffParam
            );
            this.buffs.push(toAddBuff);
            this.buffs.sort((a, b) => {
                return a.model.priority - b.model.priority;
            });
        }
        if (toRemove == false && buff.buffModel.onOccur != null) {
            buff.buffModel.onOccur(toAddBuff, modStack);
        }
        this.AttrRecheck();
    }

    ///<summary>
    ///获取角色身上对应的buffObj
    ///<param name="id">buff的model的id</param>
    ///<param name="caster">如果caster不是空，那么就代表只有buffObj.caster在caster里面的才符合条件</param>
    ///<return>符合条件的buffObj数组</return>
    ///</summary>
    public GetBuffById(id: string, caster: GameObject[] = null): BuffObj[] {
        const res: BuffObj[] = [];
        for (let i = 0; i < this.buffs.length;  i++) {
            if (this.buffs[i].model.id == id && (caster == null || caster.length <= 0 || caster.indexOf(this.buffs[i].caster) >= 0)) {
                res.push(this.buffs[i]);
            }
        }
        return res;
    }

    ///<summary>
    ///根据id获得角色学会的技能（skillObj），如果没有则返回null
    ///<param name="id">技能的id</param>
    ///<return>skillObj or null</return>
    ///</summary>
    public GetSkillById(id: string): SkillObj {
        for (let i = 0; i < this.skills.length; i++ ) {
            if (this.skills[i].model.id == id) {
                return this.skills[i];
            }
        }
        return null;
    }

    ///<summary>
    ///释放一个技能，释放技能并不总是成功的，如果你一直发释放技能的命令，那失败率应该是骤增的
    ///<param name="id">要释放的技能的id</param>
    ///<return>是否释放成功</return>
    ///</summary>
    public CastSkill(id: string): boolean {
        if (this.controlState.canUseSkill == false) return false; //不能用技能就不放了
        const skillObj = this.GetSkillById(id);
        if (skillObj == null || skillObj.cooldown > 0) return false;
        let castSuccess = false;
        if (this.resource.Enough(skillObj.model.condition) == true) {
            let timeline = new TimelineObj(
                skillObj.model.effect, this.gameObject, skillObj
            );
            for (let i = 0; i < this.buffs.length; i++) {
                if (this.buffs[i].model.onCast != null) {
                    timeline = this.buffs[i].model.onCast(this.buffs[i], skillObj, timeline);
                }
            }
            if (timeline != null) {
                this.ModResource(ChaResource.multiplyByFloat(skillObj.model.cost, -1));
                SceneVariants.CreateTimelineObj(timeline);
                castSuccess = true;
            }

        }
        skillObj.cooldown = 0.1;   //无论成功与否，都会进入gcd
        return castSuccess;
    }

    ///<summary>
    ///初始化角色的属性
    ///</summary>
    public InitBaseProp(cProp: ChaProperty): void {
        this.baseProp = cProp;
        this.AttrRecheck();
        this.resource.hp = this._prop.hp;
        this.resource.ammo = this._prop.ammo;
        this.resource.stamina = 100;
    }

    ///<summary>
    ///学习某个技能
    ///<param name="skillModel">技能的模板</param>
    ///<param name="level">技能等级</param>
    ///</summary>
    public LearnSkill(skillModel: SkillModel, level: number = 1): void {
        this.skills.push(new SkillObj(skillModel, level));
        if (skillModel.buff != null) {
            for (let i = 0; i < skillModel.buff.length; i++) {
                const abi = skillModel.buff[i];
                abi.permanent = true;
                abi.duration = 10;
                abi.durationSetTo = true;
                this.AddBuff(abi);
            }
        }
    }

    ///<summary>
    ///设置视觉元素
    ///</summary>
    public SetView(view: GameObject, animInfo: { [key: string]: AnimInfo }): void {
        if (view == null) return;
        this.synchronizedUnits();
        view.transform.SetParent(this.viewContainer.transform);
        view.transform.position = new Vector3(0, this.gameObject.transform.position.y, 0);
        this.gameObject.transform.position = new Vector3(
            this.gameObject.transform.position.x,
            0,
            this.gameObject.transform.position.z
        );
        this.gameObject.GetComponent<UnitAnim>().animInfo = animInfo;
    }

    ///<summary>
    ///设置无敌时间
    ///<param name="time">无敌的时间，单位：秒</param>
    ///</summary>
    public SetImmuneTime(time: number): void {
        this._immuneTime = Math.max(this._immuneTime, time);
    }

    ///<summary>
    ///是否拥有某个tag
    ///</summary>
    public HasTag(tag: string): boolean {
        if (this.tags == null || this.tags.length <= 0) return false;
        for (let i = 0; i < this.tags.length; i++) {
            if(this.tags[i] == tag) {
                return true;
            }
        }
        return false;
    }
}
