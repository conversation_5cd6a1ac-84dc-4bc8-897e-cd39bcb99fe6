///<summary>
///在一个gameObject下添加这个，让这个gameObject成为一个"绑点"，这样就可以在这东西里面管理一些挂载的gameObject
///最常见的用途是角色身上某个点播放视觉特效什么的。
///</summary>
export class UnitBindPoint extends MonoBehaviour {
    ///<summary>
    ///绑点的名称
    ///</summary>
    public key: string;

    ///<summary>
    ///偏移坐标
    ///</summary>
    public offset: Vector3;

    ///<summary>
    ///已经挂着的gameobject信息
    ///key就是一个索引，便于找到
    ///</summary>
    private bindGameObject: { [key: string]: BindGameObjectInfo } = {};

    private FixedUpdate(): void {
        const toRemove: string[] = [];
        for (const key in this.bindGameObject) {
            const goInfo = this.bindGameObject[key];
            if (goInfo.gameObject == null) {
                toRemove.push(key);
                continue;
            }
            if (goInfo.forever == false) {
                goInfo.duration -= Time.fixedDeltaTime;
                if (goInfo.duration <= 0) {
                    Destroy(goInfo.gameObject);
                    toRemove.push(key);
                }
            }
        }
        for (let i = 0; i < toRemove.length; i++) {
            delete this.bindGameObject[toRemove[i]];
        }
    }

    ///<summary>
    ///添加一个gameObject绑定
    ///<param name="goPath">要挂载的gameObject的prefabs路径，必须在resources下</param>
    ///<param name="key">挂载信息的key，其实就是dictionary的key，手动删除的时候要用</param>
    ///<param name="loop">是否循环播放，直到手动删除</param>
    ///</summary>
    public AddBindGameObject(goPath: string, key: string, loop: boolean): void {
        if (key != "" && key in this.bindGameObject) return;    //已经存在，加不成
        
        const effectGO = Instantiate<GameObject>(
            Resources.Load<GameObject>(goPath),
            Vector3.zero,
            Quaternion.identity,
            this.gameObject.transform
        );
        effectGO.transform.localPosition = this.offset;
        effectGO.transform.localRotation = Quaternion.identity;
        if (!effectGO) return;
        const se = effectGO.GetComponent<SightEffect>();
        if (!se) {
            Destroy(effectGO);
            return;
        } 
        const duration = se.duration * (loop == false ? 1 : -1);
        const bindGameObjectInfo = new BindGameObjectInfo(
            effectGO, duration
        );
        if (key != "") {
            this.bindGameObject[key] = bindGameObjectInfo;
        } else {
            this.bindGameObject[
                Time.frameCount * Math.random() * 8.99 + 1 + "_" + Math.floor(Math.random() * 8999 + 1)
            ] = bindGameObjectInfo;
        }
            
    }

    ///<summary>
    ///移除一个gameObject的绑定
    ///<param name="key">挂载信息的key，其实就是dictionary的key</param>
    ///</summary>
    public RemoveBindGameObject(key: string): void {
        if (!(key in this.bindGameObject)) return;
        if (this.bindGameObject[key].gameObject) {
            Destroy(this.bindGameObject[key].gameObject);
        }
        delete this.bindGameObject[key];
    }
}

///<summary>
///被挂载的gameobject的记录
///</summary>
export class BindGameObjectInfo {
    ///<summary>
    ///gameObject的地址
    ///</summary>
    public gameObject: GameObject;

    ///<summary>
    ///还有多少时间之后被销毁，单位：秒
    ///</summary>
    public duration: number;

    ///<summary>
    ///有些是不能被销毁的，得外部控制销毁，所以永久存在
    ///</summary>
    public forever: boolean;

    ///<summary>
    ///<param name="gameObject">要挂载的gameObject</param>
    ///<param name="duration">挂的时间，时间到了销毁，[Magic]如果<=0则代表永久</param>
    ///</summary>
    constructor(gameObject: GameObject, duration: number) {
        this.gameObject = gameObject;
        this.duration = Math.abs(duration);
        this.forever = duration <= 0;
    }
}
