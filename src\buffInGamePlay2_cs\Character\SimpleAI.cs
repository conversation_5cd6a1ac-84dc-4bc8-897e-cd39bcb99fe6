///<summary>
///顾名思义，就是为了凑效果的，并不是真的ai结构，只是让敌人看起来运动了
///</summary>
export class SimpleAI extends MonoBehaviour {
    private toNextFire: number = 3.0;
    private toNextRotate: number = 2.0;

    private moveDegree: number;

    private fire: TimelineModel = new TimelineModel("", [
        new TimelineNode(0.00, "SetCasterControlState", true, true, false),
        new TimelineNode(0.00, "CasterPlayAnim", "Fire", false),
        new TimelineNode(0.10, "PlaySightEffectOnCaster", "Muzzle","Effect/MuzzleFlash","",false),
        new TimelineNode(0.10, "FireBullet", 
            new BulletLauncher(
                DesingerTables.Bullet.data["normal1"], null, Vector3.zero, 0, 6.0, 10.0, 0, 
                null, null, false
            ), "Muzzle"
        ),
        new TimelineNode(0.50, "SetCasterControlState", true, true, true)
    ], 0.50, TimelineGoTo.Null);

    private chaState: ChaState;

    Start(): void {
        this.chaState = this.gameObject.GetComponent<ChaState>();
        this.moveDegree = this.transform.rotation.eulerAngles.y;   
    }

    private FixedUpdate(): void {
        if (!this.chaState || this.chaState.dead == true) return;

        const timePassed = Time.fixedDeltaTime;

        const faceVec = Vector3.subtract(SceneVariants.MainActor().transform.position, this.transform.position);
        const rotateTo = Math.atan2(faceVec.x, faceVec.z) * 180.00 / Math.PI;
        this.toNextRotate -= timePassed;
        if (this.toNextRotate <= 0) {
           this.moveDegree += Random.Range(-90.00, 90.00);
           this.toNextRotate = Random.Range(1.60, 3.20);
        }
        this.chaState.OrderRotateTo(rotateTo);
        const rRadius = this.moveDegree * Math.PI / 180;

        const mSpd = this.chaState.moveSpeed;
        const mInfo = new Vector3(
            Math.sin(rRadius) * mSpd,
            0,
            Math.cos(rRadius) * mSpd
        );
        this.chaState.OrderMove(mInfo);

        this.toNextFire -= timePassed;
        if (this.toNextFire <= 0) {
            SceneVariants.CreateTimeline(this.fire, this.gameObject, null);
            this.toNextFire = Random.Range(2.00, 5.00);
        }
    }
}
