///<summary>
///预约了多少时间内【匀速直线】移动往某个方向多远
///</summary>
export class MovePreorder {
    ///<summary>
    ///想要移动的方向和距离
    ///</summary>
    public velocity: Vector3;

    ///<summary>
    ///多久完成，单位秒
    ///</summary>
    private inTime: number;

    ///<summary>
    ///还有多久移动完成，单位：秒，如果小于1帧的时间但还大于0，就会当做1帧来执行
    ///</summary>
    public duration: number;
    
    constructor(velocity: Vector3, duration: number) {
        this.velocity = velocity;
        this.duration = duration;
        this.inTime = duration;
    }

    ///<summary>
    ///运行了一段时间，返回这段时间内的移动力
    ///<param name="time">运行的时间，单位：秒</param>
    ///<return>移动力</return>
    public VeloInTime(time: number): Vector3 {
        if (time >= this.duration) {
            this.duration = 0;
        } else {
            this.duration -= time;
        }
        return this.inTime <= 0 ? this.velocity : Vector3.multiplyByFloat(this.velocity, 1 / this.inTime);
    }
}

export enum MoveType {
    ground = 0,
    fly = 1
}
