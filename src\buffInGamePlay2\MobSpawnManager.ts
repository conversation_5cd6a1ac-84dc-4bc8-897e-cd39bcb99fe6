///<summary>
///我专门管刷怪的
///</summary>
export class MobSpawnManager extends MonoBehaviour {
    //[Tooltip("保持最多有多少个怪物的数量，一旦超过就不刷了\n如果数字太大，一切后果概不负责……")]
    public maxMob: number;

    //[Tooltip("刷怪的周期，现在肯定是均匀的，不高兴取随机数了，就这么地了")]
    public spawnPeriod: number = 10.0;   //10秒检查一次

    private timePassed: number = 0;
    private justCreated: boolean = true;
    private spawned: number = 0;    //已经刷过多少个了

    private static mobSide: number = 2;

    private FixedUpdate(): void {
        if (this.justCreated == true && this.maxMob > 0) {
            this.Spawn();
        }
        this.timePassed += Time.fixedDeltaTime;
        if (this.timePassed >= this.spawnPeriod) {
            this.timePassed = 0;
            this.Spawn();
        }
    }

    private Spawn(): void {
        const cha = GameObject.FindGameObjectsWithTag("Character");
        let toSpawn = this.maxMob;
        for (let i = 0; i < cha.length; i++) {
            const cs = cha[i].GetComponent<ChaState>();
            if (cs != null && cs.dead == false && cs.side == MobSpawnManager.mobSide) {
                toSpawn -= 1;
            }
        }
        for (let i = 0; i < toSpawn; i++) {
            const enemy = SceneVariants.CreateCharacter(
                "MaleGunner", MobSpawnManager.mobSide, 
                SceneVariants.map.GetRandomPosForCharacter(new RectInt(0, 0, SceneVariants.map.MapWidth(), SceneVariants.map.MapHeight())),
                new ChaProperty(Random.Range(50,70), 50 + this.spawned * 2, 0, Random.Range(15,30) + this.spawned, 100, 0.25, 0.4), Random.Range(0.00, 359.99)
            );
            enemy.AddComponent<SimpleAI>();
            this.spawned += 1;
        }
    }
}
