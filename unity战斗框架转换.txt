
Trae

1.同理,参考ChaSkill.cs和ChaProperty.cs和ChaControlState.cs代码,翻译成对应的TypeScript,生成到D:\trunk\w6d\mobile_module2\src\com\buffInGamePlay对应目录下,例如Buff-In-TopDownShooter\Assets\Scripts\Structs对应buffInGamePlay\structs目录,
2.遇到struct用ts的class代替
3.不要优化代码,尽量保留原本c#的结构,例如方法里的实现,原本用for循环,就尽量保留
4.不要删除注释
5.遇到陌生的结构不要替换,假设已经在别的文件实现

Augment

1.参考Buff-In-TopDownShooter\Assets\Scripts\Structs\Character目录下的c#代码,
    翻译成对应的TypeScript,生成到src\buffInGamePlay2\Character目录下,
2.遇到struct用ts的class代替
3.不要优化代码,尽量保留原本c#的结构,例如方法里的实现,原本用for循环,就尽量保留
4.不要删除注释
5.遇到陌生的结构不要替换,假设已经在别的文件实现,不要刻意为了追求"没有报错"
6.unity的委托使用ts的自定义type来转换

1.干的很好,请帮忙继续按照规则,
生成Buff-In-TopDownShooter\Assets\Scripts\Structs目录下的c#代码到对应src\buffInGamePlay2\Structs目录下,
已存在的ts代码就不要重新生成了
2.遇到struct用ts的class代替
3.不要优化代码,尽量保留原本c#的结构,例如方法里的实现,原本用for循环,就尽量保留
4.不要删除注释
5.遇到陌生的结构不要替换,假设已经在别的文件实现,不要刻意为了追求"没有报错"
6.unity的委托使用ts的自定义type来转换


1.干的很好,请帮忙继续按照规则,
生成Buff-In-TopDownShooter\Assets\Scripts\Unit目录下的c#代码到对应src\buffInGamePlay2\Unit目录下,
同样生成Buff-In-TopDownShooter\Assets\Scripts目录中的代码文件:AoeManager.cs, BulletManager.cs, BulletManager.cs, CamFollow.cs, DamageManager.cs, GameManager.cs, MobSpawnManager.cs, TimelineManager.cs
已存在的ts代码就不要重新生成了
2.遇到struct用ts的class代替
3.不要优化代码,尽量保留原本c#的结构,例如方法里的实现,原本用for循环,就尽量保留
4.不要删除注释
5.遇到陌生的结构不要替换,假设已经在别的文件实现,不要刻意为了追求"没有报错"
6.unity的委托使用ts的自定义type来转换
