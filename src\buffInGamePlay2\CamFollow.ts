export class CamFollow extends MonoBehaviour {
    //镜头跟随的偏移
    private offset: Vector3;

    //镜头要跟随的角色，没有角色镜头就停止跟随了
    private followCharacter: GameObject;
    
    LateUpdate(): void {
        if (!this.followCharacter) return;
        this.transform.position = Vector3.add(this.offset, this.followCharacter.transform.position);
    }

    public SetFollowCharacter(cha: GameObject): void {
        this.followCharacter = cha;
        this.offset = new Vector3(
            this.transform.position.x - cha.transform.position.x,
            -this.transform.position.z / Math.cos(this.transform.rotation.eulerAngles.x * Math.PI / 180) - cha.transform.position.y,
            this.transform.position.z - cha.transform.position.z
        );
    }
}
