///<summary>
///管理游戏中所有的timeline
///</summary>
export class TimelineManager extends MonoBehaviour {
    private timelines: TimelineObj[] = [];

    private FixedUpdate(): void {
        if (this.timelines.length <= 0) return;

        let idx = 0;
        while (idx < this.timelines.length) {
            const wasTimeElapsed = this.timelines[idx].timeElapsed;
            this.timelines[idx].timeElapsed += Time.fixedDeltaTime * this.timelines[idx].timeScale;

            //判断有没有返回点
            if (
                this.timelines[idx].model.chargeGoBack.atDuration < this.timelines[idx].timeElapsed &&
                this.timelines[idx].model.chargeGoBack.atDuration >= wasTimeElapsed
            ) {
                if (this.timelines[idx].caster) {
                    const cs = this.timelines[idx].caster.GetComponent<ChaState>();
                    if (cs.charging == true) {
                        this.timelines[idx].timeElapsed = this.timelines[idx].model.chargeGoBack.gotoDuration;
                        continue;
                    }
                }
            }
            //执行时间点内的事情
            for (let i = 0; i < this.timelines[idx].model.nodes.length; i++) {
                if (
                    this.timelines[idx].model.nodes[i].timeElapsed < this.timelines[idx].timeElapsed &&
                    this.timelines[idx].model.nodes[i].timeElapsed >= wasTimeElapsed
                ) {
                    this.timelines[idx].model.nodes[i].doEvent(
                        this.timelines[idx], 
                        ...this.timelines[idx].model.nodes[i].eveParams
                    );
                }
            }

            //判断timeline是否终结
            if (this.timelines[idx].model.duration <= this.timelines[idx].timeElapsed) {
                this.timelines.splice(idx, 1);
            } else {
                idx++;
            }
        }
    }

    ///<summary>
    ///添加一个timeline
    ///<param name="timelineModel">要添加的timeline的model</param>
    ///<param name="caster">timeline的负责人</param>
    ///<param name="source">添加的源数据，比如技能就是skillObj</param>
    ///</summary>
    public AddTimeline(timelineModel: TimelineModel, caster: GameObject, source: any): void;
    public AddTimeline(timeline: TimelineObj): void;
    public AddTimeline(timelineModelOrObj: TimelineModel | TimelineObj, caster?: GameObject, source?: any): void {
        if (timelineModelOrObj instanceof TimelineObj) {
            // 重载：添加一个timeline对象
            const timeline = timelineModelOrObj as TimelineObj;
            if (timeline.caster != null && this.CasterHasTimeline(timeline.caster) == true) return;
            this.timelines.push(timeline);
        } else {
            // 重载：从model创建timeline
            const timelineModel = timelineModelOrObj as TimelineModel;
            if (this.CasterHasTimeline(caster) == true) return;
            this.timelines.push(new TimelineObj(timelineModel, caster, source));
        }
    }

    public CasterHasTimeline(caster: GameObject): boolean {
        for (let i = 0; i < this.timelines.length; i++) {
            if (this.timelines[i].caster == caster) return true;
        }
        return false;
    }
}
