///<summary>
///单个动画的信息
///</summary>
export class AnimInfo {
    ///<summary>
    ///这个动画的key，相当于一个id
    ///</summary>
    public key: string;

    ///<summary>
    ///动画优先级，因为本质是个回合制游戏，所以播放动作有优先级高的优先播放一说。
    ///比如受伤通常就会低于攻击，因为角色很可能同时受伤和发动攻击，但是发动攻击并不会因为受伤终止，比如wow里面。
    ///</summary>
    public priority: number;

    ///<summary>
    ///这个动画有哪些可能性。
    ///最常见的比如受伤动画可能有3种，但其实出哪一种没有逻辑讲究，就说明这个值有3条。
    ///但如果受伤动画是根据受到伤害数量来定的，说明有逻辑，应该有3条SingleAnimInfo，他们的key都只有1个受伤动作。
    ///key（SingleAnimInfo）是animator里面的动画信息
    ///value（uint）是权重
    ///</summary>
    public animations: { key: SingleAnimInfo, value: number }[];


    public static Null = new AnimInfo("", null, 0);

    constructor(key: string, animations: { key: SingleAnimInfo, value: number }[], priority: number = 0) {
        this.animations = animations;
        this.priority = priority;
        this.key = key;
    }


    ///<summary>
    ///随机获得一个动画信息（animator里的动画名字等）
    ///<return>动画信息</return>
    ///</summary>
    public RandomKey(): SingleAnimInfo {
        if (this.animations.length <= 0) return SingleAnimInfo.Null;

        if (this.animations.length == 1) return this.animations[0].key;
        
        let totalV = 0;
        for (let i = 0; i < this.animations.length; i++) {
            totalV += this.animations[i].value;
        }
        if (totalV <= 0) return SingleAnimInfo.Null;


        let rv = Math.floor(Math.random() * totalV);
        let rIndex = 0;
        while (rv > 0) {
            rv -= this.animations[rIndex].value;
            rIndex += 1;
        }
        rIndex = Math.min(rIndex, this.animations.length - 1);
        return this.animations[rIndex].key;
    }

    ///<summary>
    ///是否存在一个动画的名字动画名字（animator里的动画名字）
    ///<param name="animName">animator中动画的名字</param>
    ///<return>如果是true就存在</return>
    ///</summary>
    public ContainsAnim(animName: string): boolean {
        for (let i = 0; i < this.animations.length; i++) {
            if (this.animations[i].key.animName == animName) return true;
        }
        return false;
    }
}

///<summary>
///单个动画信息，主要是在animator中的name，以及多久以后回到可以被改写的程度
///</summary>
export class SingleAnimInfo {
    ///<summary>
    ///animator中的名称
    ///</summary>
    public animName: string;

    ///<summary>
    ///在多久之后权重清0，单位秒
    ///</summary>
    public duration: number;

    constructor(animName: string, duration: number = 0) {
        this.animName = animName;
        this.duration = duration;
    }

    public static Null = new SingleAnimInfo("", 0);

}
