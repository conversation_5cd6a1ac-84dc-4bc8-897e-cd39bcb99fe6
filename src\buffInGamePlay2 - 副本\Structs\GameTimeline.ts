///<summary>
///注意：和unity的timeline不是一个东西，这个概念出来的时候unity都还没出来。
///这是一段预约的事情的记录，也就是当timelineObj产生之后，就会开始计时，并且在每个"关键帧"（类似flash的概念）做事情。
///所有的道具使用效果、技能效果都可以抽象为一个timeline，由timeline来"指导"后续的事件发生。
///</summary>
export class TimelineObj {
    ///<summary>
    ///Timeline的基础信息
    ///</summary>
    public model: TimelineModel;
    

    ///<summary>
    ///Timeline的焦点对象也就是创建timeline的负责人，比如技能产生的timeline，就是技能的施法者
    ///</summary>
    public caster: GameObject;

    ///<summary>
    ///倍速，1=100%，0.1=10%是最小值
    ///</summary>
    public get timeScale(): number {
        return this._timeScale;
    }
    
    public set timeScale(value: number) {
        this._timeScale = Math.max(0.100, value);
    }
    
    private _timeScale: number = 1.00;

    ///<summary>
    ///Timeline的创建参数，如果是一个技能，这就是一个skillObj
    ///</summary>
    public param: any;

    ///<summary>
    ///Timeline已经运行了多少秒了
    ///</summary>
    public timeElapsed: number = 0;

    

    ///<summary>
    ///一些重要的逻辑参数，是根据游戏机制在程序层提供的，这里目前需要的是
    ///[faceDegree] 发生时如果有caster，则caster企图面向的角度（主动）。
    ///[moveDegree] 发生时如果有caster，则caster企图移动向的角度（主动）。
    ///</summary>
    public values: { [key: string]: any };

    constructor(model: TimelineModel, caster: GameObject, param: any) {
        this.model = model;
        this.caster = caster;
        this.values = {}; 
        this._timeScale = 1.00;
        if (caster) {
            const cs = caster.GetComponent<ChaState>();
            if (cs) {
                this.values["faceDegree"] = cs.faceDegree;
                this.values["moveDegree"] = cs.moveDegree;
            }
            this._timeScale = cs.actionSpeed;
        }
        this.param = param;
    }

    ///<summary>
    ///尝试从values获得某个值
    ///<param name="key">这个值的key{faceDegree, moveDegree}</param>
    ///<return>取出对应的值，如果不存在就是null</return>
    ///</summary>
    public GetValue(key: string): any {
        if (!(key in this.values)) return null;
        return this.values[key];
    }
}

///<summary>
///策划预先填表制作的，就是这个东西，同样她也是被clone到obj当中去的
///</summary>
export class TimelineModel {
    public id: string;

    ///<summary>
    ///Timeline运行多久之后发生，单位：秒
    ///</summary>
    public nodes: TimelineNode[];

    ///<summary>
    ///Timeline一共多长时间（到时间了就丢掉了），单位秒
    ///</summary>
    public duration: number;

    ///<summary>
    ///如果有caster，并且caster处于蓄力状态，则可能会经历跳转点
    ///</summary>
    public chargeGoBack: TimelineGoTo;

    constructor(id: string, nodes: TimelineNode[], duration: number, chargeGoBack: TimelineGoTo) {
        this.id = id;
        this.nodes = nodes;
        this.duration = duration;
        this.chargeGoBack = chargeGoBack;
    }
}

///<summary>
///Timeline每一个节点上要发生的事情
///</summary>
export class TimelineNode {
    ///<summary>
    ///Timeline运行多久之后发生，单位：秒
    ///</summary>
    public timeElapsed: number;

    ///<summary>
    ///要执行的脚本函数
    ///</summary>
    public doEvent: TimelineEvent;

    ///<summary>
    ///要执行的函数的参数
    ///</summary>
    public readonly eveParams: any[];

    constructor(time: number, doEve: string, ...eveArgs: any[]) {
        this.timeElapsed = time;
        this.doEvent = DesignerScripts.Timeline.functions[doEve];
        this.eveParams = eveArgs;
    }
}

///<summary>
///Timeline的一个跳转点信息
///</summary>
export class TimelineGoTo {
    ///<summary>
    ///自身处于时间点
    ///</summary>
    public atDuration: number;

    ///<summary>
    ///跳转到时间点
    ///</summary>
    public gotoDuration: number;

    constructor(atDuration: number, gotoDuration: number) {
        this.atDuration = atDuration;
        this.gotoDuration = gotoDuration;
    }

    public static Null = new TimelineGoTo(Number.MAX_VALUE, Number.MAX_VALUE);
}

export type TimelineEvent = (timeline: TimelineObj, ...args: any[]) => void;
