//地图的每个单元格的信息
export class GridInfo {
    ///<summary>
    ///位于resources/prefabs文件夹下的的位置
    ///比如是resources/prefabs/terrain/grass，这个值就是"terrain/grass"
    ///</summary>
    public prefabPath: string;

    ///<summary>
    ///地面移动是否可以通过
    ///</summary>
    public groundCanPass: boolean;

    ///<summary>
    ///飞行是否可以通过
    ///</summary>
    public flyCanPass: boolean;

    constructor(prefabPath: string, characterCanPass: boolean = true, bulletCanPass: boolean = true) {
        this.prefabPath = prefabPath;
        this.groundCanPass = characterCanPass;
        this.flyCanPass = bulletCanPass;
    }

    ///<summary>
    ///场景外的格子，无意义的单元格
    ///</summary>
    public static readonly VoidGrid = new GridInfo("", false, false);
}

//整个地图的信息
export class MapInfo {
    ///<summary>
    ///地图的单元格信息，对应角色坐标为[x,z]
    ///</summary>
    public grid: GridInfo[][];

    ///<summary>
    ///每个单元格的宽度和高度
    ///单位：米
    ///</summary>
    public readonly gridSize: Vector2;

    ///<summary>
    ///获得地图的边界，单位：米
    ///</summary>
    public readonly border: Rect;


    constructor(map: GridInfo[][], gridSize: Vector2) {
        this.grid = map;
        this.gridSize = new Vector2(
            Math.max(0.1, gridSize.x),    //最小0.1米
            Math.max(0.1, gridSize.y)
        );
        this.border = new Rect(
            -gridSize.x / 2.00,
            -gridSize.y / 2.00,
            gridSize.x * this.MapWidth(),
            gridSize.y * this.MapHeight()
        );
    }

    

    ///<summary>
    ///地图的宽度
    ///<return>单位：单元格</return>
    ///</summary>
    public MapWidth(): number {
        return this.grid.length;
    }

    ///<summary>
    ///地图的高度
    ///<return>单位：单元格</return>
    ///</summary>
    public MapHeight(): number {
        return this.grid[0].length;
    }

    ///<summary>
    ///获得某个东西坐落在的位置信息
    ///<param name="pos">要检查的点，单位：米</param>
    ///<return>返回这个点的单元格信息</return>
    ///</summary>
    public GetGridInPosition(pos: Vector3): GridInfo {
        const gPos = this.GetGridPosByMeter(pos.x, pos.z);
        if (gPos.x < 0 || gPos.x >= this.MapWidth() || gPos.y < 0 || gPos.y >= this.MapHeight()) 
            return GridInfo.VoidGrid;
        return this.grid[gPos.x][gPos.y];
    }

    ///<summary>
    ///从地图上x坐标的米，获得单元格坐标x
    ///<param name="x">角色坐标x，单位：米</param>
    ///<param name="z">角色坐标z，单位：米</param>
    ///<return>单元格坐标，单位：单元格</return>
    ///</summary>
    public GetGridPosByMeter(x: number, z: number): Vector2Int {
        return new Vector2Int(
            //Math.floor((x + this.gridSize.x / 2.00) / this.gridSize.x),
            //Math.floor((z + this.gridSize.y / 2.00) / this.gridSize.y)
            Math.round(x / this.gridSize.x),
            Math.round(z / this.gridSize.y)
        );
    }

    ///<summary>
    ///判断某种移动模式下，某个单元格是否可过
    ///<param name="gridX">单元格坐标x</param>
    ///<param name="gridY">单元格坐标y</param>
    ///<param name="moveType">移动方式</param>
    ///<param name="ignoreBorder">是否把地图外的区域都当做可过</param>
    ///<return>是否可过</return>
    ///</summary>
    public CanGridPasses(gridX: number, gridY: number, moveType: MoveType, ignoreBorder: boolean): boolean {
        if (gridX < 0 || gridX >= this.MapWidth() || gridY < 0 || gridY >= this.MapHeight()) return ignoreBorder;
        switch (moveType) {
            case MoveType.ground: return this.grid[gridX][gridY].groundCanPass;
            case MoveType.fly: return this.grid[gridX][gridY].flyCanPass;
        }
        return false;
    }

    ///<summary>
    ///判断一个单位是否可以站在某个位置上
    ///<param name="pos">单位的位置，单位：米</param>
    ///<param name="radius">单位的半径，单位：米</param>
    ///<param name="moveType">单位移动模式</param>
    ///<return>是否可以站在这里，true代表可以</return>
    ///</summary>
    public CanUnitPlacedHere(pos: Vector3, radius: number, moveType: MoveType): boolean {
        const lt = this.GetGridPosByMeter(pos.x - radius, pos.z - radius);
        const rb = this.GetGridPosByMeter(pos.x + radius, pos.z + radius);
        const aw = rb.x - lt.x + 1;
        const ah = rb.y - lt.y + 1;
        const collisionRects: Rect[] = [];
        for (let i = lt.x; i <= rb.x; i++) {
            for (let j = lt.y; j <= rb.y; j++) {
                if (this.CanGridPasses(i, j, moveType, false) == false) {
                    collisionRects.push(new Rect(
                        (i - 0.5) * this.gridSize.x,
                        (j - 0.5) * this.gridSize.y,
                        this.gridSize.x,
                        this.gridSize.y
                    ));
                }
                
            }
        }
        return !Utils.CircleHitRects(new Vector2(pos.x, pos.z), radius, collisionRects);
        // const gPos = this.GetGridPosByMeter(pos.x, pos.z);
        // if (gPos.x < 0 || gPos.y < 0 || gPos.x >= this.MapWidth() || gPos.y >= this.MapHeight()) return false;
        // return this.grid[gPos.x][gPos.y].groundCanPass;
    }


    ///<summary>
    ///随机获得一个坐标，可以让角色站立在那里
    ///获得的坐标单位是米的，而非单元格，其坐标y始终为0
    ///如果实在没有这个位置，就会返回vector3.zero
    ///<param name="range">一个随机的范围，单位：单元格</param>
    ///<param name="chaRadius">角色半径，单位：米</param>
    ///<param name="moveType">单位移动模式</param>
    ///<return>可以落脚的坐标点</return>
    public GetRandomPosForCharacter(range: RectInt, chaRadius: number = 0.00, moveType: MoveType = MoveType.ground): Vector3 {
        const mayRes: Vector3[] = [];
        for (let i = range.x; i < range.x + range.width; i++) {
            for (let j = range.y; j < range.y + range.height; j++) {
                //if (i >= 0 && i < this.MapWidth() && j >= 0 && j < this.MapHeight() && gridInfo[i, j].characterCanPass == true){
                const ranPos = new Vector3(
                    i * this.gridSize.x,
                    0,
                    j * this.gridSize.y
                );
                if (this.CanUnitPlacedHere(ranPos, chaRadius, moveType) == true) {
                    mayRes.push(ranPos);
                }
            }
        }
        return mayRes[Math.floor(Math.random() * mayRes.length)];
    }



    ///<summary>
    ///从一个点（单位米）出发获得方向上的第一个竖着的阻挡
    ///<param name="pivot">出发的点，单位：米</param>
    ///<param name="dir">查询方向以及长度，单位：米</param>
    ///<param name="radius">假设有一个半径（当做点是圆形中心），也就是额外追加一个距离，单位：米</param>
    ///<param name="moveType">移动方式</param>
    ///<param name="ignoreBorder">是否把地图外的区域都当做可过</param>
    ///<return>最合适的x坐标</return>
    ///</summary>
    public GetNearestVerticalBlock(pivot: Vector3, dir: number, radius: number, moveType: MoveType, ignoreBorder: boolean): number {
        if (dir == 0) return pivot.x;
        const dv = dir > 0 ? 1 : -1;
        const bestX = pivot.x + dir;
        const seekWidth = Math.ceil((Math.abs(dir) + radius) / this.gridSize.x + 2);
        const gPos = this.GetGridPosByMeter(pivot.x, pivot.z);
        for (let i = 0; i < seekWidth; i++) {
            const cgX = gPos.x + dv * i;
            if (this.CanGridPasses(cgX, gPos.y, moveType, ignoreBorder) == false) {
                const wallX = (cgX - dv * 0.5) * this.gridSize.x - dv * radius;
                if (dv > 0) {
                    return Math.min(wallX, bestX);
                } else {
                    return Math.max(wallX, bestX);
                }
            }
        }
        return bestX;
    }

    ///<summary>
    ///从一个点（单位米）出发获得方向上的第一个横着的阻挡
    ///<param name="pivot">出发的点，单位：米</param>
    ///<param name="dir">查询方向以及高度，单位：米</param>
    ///<param name="radius">假设有一个半径（当做点是圆形中心），也就是额外追加一个距离，单位：米</param>
    ///<param name="moveType">移动方式</param>
    ///<param name="ignoreBorder">是否把地图外的区域都当做可过</param>
    ///<return>最合适的z坐标</return>
    ///</summary>
    public GetNearestHorizontalBlock(pivot: Vector3, dir: number, radius: number, moveType: MoveType, ignoreBorder: boolean): number {
        if (dir == 0) return pivot.z;
        const dv = dir > 0 ? 1 : -1;
        const bestZ = pivot.z + dir;
        const seekHeight = Math.ceil((Math.abs(dir) + radius) / this.gridSize.y + 2);
        const gPos = this.GetGridPosByMeter(pivot.x, pivot.z);
        for (let i = 0; i < seekHeight; i++) {
            const cgY = gPos.y + dv * i;
            if (this.CanGridPasses(gPos.x, cgY, moveType, ignoreBorder) == false) {
                const wallZ = (cgY - dv * 0.5) * this.gridSize.y - dv * radius;
                if (dv > 0) {
                    return Math.min(wallZ, bestZ);
                } else {
                    return Math.max(wallZ, bestZ);
                }
            }
        }
        return bestZ;
    }

    ///<summary>
    ///根据一个圆（中心点和半径），获得期望移动到某个坐标的最理想的点
    ///TODO 只有移动速度不那么快的时候才在大多数时候工作正常……
    ///<param name="pivot">这个圆形的中心点坐标</param>
    ///<param name="radius">这个圆形的半径</param>
    ///<param name="targetPos">这个圆形期望移动到的坐标</param>
    ///<param name="moveType">圆形的移动方式</param>
    ///<param name="ignoreBorder">是否把地图外的区域都当做可过</param>
    ///<return>应该移动到的坐标</return>
    ///</summary>
    public FixTargetPosition(pivot: Vector3, radius: number, targetPos: Vector3, moveType: MoveType, ignoreBorder: boolean): MapTargetPosInfo {
        const xDir = targetPos.x - pivot.x;
        const zDir = targetPos.z - pivot.z;
        const bestX = this.GetNearestVerticalBlock(pivot, xDir, radius, moveType, ignoreBorder);
        const bestZ = this.GetNearestHorizontalBlock(pivot, zDir, radius, moveType, ignoreBorder);

        const obstacled = (
            Math.round(bestX * 1000) != Math.round(targetPos.x * 1000) ||
            Math.round(bestZ * 1000) != Math.round(targetPos.z * 1000)
        );
        return new MapTargetPosInfo(obstacled, new Vector3(bestX, targetPos.y, bestZ));
    }
}

///<summary>
///目标地点的信息
///</summary>
export class MapTargetPosInfo {
    ///<summary>
    ///是否会碰到阻碍
    ///</summary>
    public obstacle: boolean;

    ///<summary>
    ///建议移动到的位置
    ///</summary>
    public suggestPos: Vector3;

    constructor(obstacle: boolean, suggestPos: Vector3) {
        this.obstacle = obstacle;
        this.suggestPos = suggestPos;
    }
}
