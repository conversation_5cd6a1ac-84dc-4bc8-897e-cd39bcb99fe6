export class GameManager extends MonoBehaviour {
    //总有一个角色是主角，也就是玩家控制的，并且镜头跟随的
    public get mainActor(): GameObject {
        return this.mainCharacter;
    }
    private mainCharacter: GameObject;  

    //所有的gameobject放置的地方
    private root: GameObject;

    //特效管理器
    private sightEffect: { [key: string]: GameObject } = {};


    // Start is called before the first frame update
    Start(): void {
        this.root = GameObject.Find("GameObjectLayer");

        //创建地图
        SceneVariants.RandomMap(Random.Range(10, 15), Random.Range(10, 15));
        this.CreateMapGameObjects();

        //创建主角
        const playerPos = SceneVariants.map.GetRandomPosForCharacter(new RectInt(0, 0, SceneVariants.map.MapWidth(), SceneVariants.map.MapHeight()));
        this.mainCharacter = this.CreateCharacter(
            "FemaleGunner", 1, new Vector3(), new ChaProperty(100, Random.Range(5000,7000), 600, Random.Range(50,70)), 0
        );  //这里必须是new Vector3()因为相机跟随的设置问题
        this.mainCharacter.AddComponent<PlayerController>().mainCamera = Camera.main;
        
        //镜头跟随
        GameObject.Find("Main Camera").GetComponent<CamFollow>().SetFollowCharacter(this.mainCharacter);   
        //ui血量捕捉，别问我这是什么狗屎了，我也觉得狗屎
        GameObject.Find("PlayerHP").GetComponent<PlayerStateListener>().playerGameObject = this.mainCharacter;

        //再设置主角位置
        this.mainCharacter.transform.position = playerPos;  
        const mcs = this.mainCharacter.GetComponent<ChaState>();
        mcs.LearnSkill(DesingerTables.Skill.data["fire"]);
        mcs.LearnSkill(DesingerTables.Skill.data["roll"]);
        mcs.LearnSkill(DesingerTables.Skill.data["spaceMonkeyBall"]);  
        mcs.LearnSkill(DesingerTables.Skill.data["homingMissle"]);   
        mcs.LearnSkill(DesingerTables.Skill.data["cloakBoomerang"]);
        mcs.LearnSkill(DesingerTables.Skill.data["teleportBullet"]);
        mcs.LearnSkill(DesingerTables.Skill.data["grenade"]);
        mcs.LearnSkill(DesingerTables.Skill.data["explosiveBarrel"]);

        //【test】给主角添加火焰护盾的aoe
        // this.CreateAoE(new AoeLauncher(
        //     DesingerTables.AoE.data["BulletShield"], this.mainCharacter, this.mainCharacter.transform.position,
        //     0.3, 600.00, 0,
        //     DesignerScripts.AoE.aoeTweenFunc["AroundCaster"],//DesignerScripts.AoE.aoeTweenFunc["AroundCaster"], 
        //     [1.2, 360.0]
        // ));
        
    }

    Update(): void {
        
    }

    private FixedUpdate(): void {
        //管理一下视觉特效，看哪些需要清楚了
        const toRemoveKey: string[] = [];
        for (const key in this.sightEffect) {
            if (this.sightEffect[key] == null) toRemoveKey.push(key);
        }
        for (let i = 0; i < toRemoveKey.length; i++) delete this.sightEffect[toRemoveKey[i]];

        
    }


    //根据prefab下的资源创建东西
    private CreateFromPrefab(prefabPath: string, position: Vector3 = new Vector3(), rotation: number = 0.00): GameObject {
        const go = Instantiate<GameObject>(
            Resources.Load<GameObject>("Prefabs/" + prefabPath),
            position,
            Quaternion.identity
        );
        if (rotation != 0) {
            go.transform.Rotate(new Vector3(0, rotation, 0));
        }
        go.transform.SetParent(this.root.transform);
        return go;
    }

    //根据global.map制作地图的prefabs
    private CreateMapGameObjects(): void {
        const mt = GameObject.FindGameObjectsWithTag("MapTile");
        for (let i = 0; i < mt.length; i++) {
            Destroy(mt[i]);
        }

        for (let i = 0; i < SceneVariants.map.MapWidth(); i++) {
            for (let j = 0; j < SceneVariants.map.MapHeight(); j++) {
                this.CreateFromPrefab(SceneVariants.map.grid[i][j].prefabPath, new Vector3(i, 0, j));
            }
        }
    }

    ///<summary>
    ///创建一个子弹对象在场景上
    ///<param name="bulletLauncher">子弹发射器</param>
    ///</summary>
    public CreateBullet(bulletLauncher: BulletLauncher): void {
        //创建一个bulletObj，这是个"空"的子弹，其实也就是没有视觉效果，其他都有了
        const bulletObj = Instantiate<GameObject>(
            Resources.Load<GameObject>("Prefabs/Bullet/BulletObj"),
            bulletLauncher.firePosition,
            Quaternion.identity,
            this.root.transform
        );

        //处理bulletObj的数据
        bulletObj.transform.RotateAround(bulletObj.transform.position, Vector3.up, bulletLauncher.fireDegree);

        bulletObj.GetComponent<BulletState>().InitByBulletLauncher(
            bulletLauncher,
            GameObject.FindGameObjectsWithTag("Character") //我这个游戏里，只给你角色对象，你要跟踪子弹，那就再把子弹也抓进来就好
        );
    }

    ///<summary>
    ///删除一个存在的子弹Object
    ///<param name="aoe">子弹的GameObject</param>
    ///<param name="immediately">是否当场清除，如果false，就是把时间变成0</param>
    ///</summary>
    public RemoveBullet(bullet: GameObject, immediately: boolean = false): void {
        if (!bullet) return;
        const bulletState = bullet.GetComponent<BulletState>();
        if (!bulletState) return;
        bulletState.duration = 0;
        if (immediately == true) {
            if (bulletState.model.onRemoved != null) {
                bulletState.model.onRemoved(bullet);
            }
            Destroy(bullet);
        }
    }

    ///<summary>
    ///创建一个aoe对象在场景上
    ///<param name="aoeLauncher">aoe的创建信息</param>
    ///</summary>
    public CreateAoE(aoeLauncher: AoeLauncher): void {
        //创建一个bulletObj，这是个"空"的子弹，其实也就是没有视觉效果，其他都有了
        const aoeObj = Instantiate<GameObject>(
            Resources.Load<GameObject>("Prefabs/Effect/AoeObj"),
            aoeLauncher.position,
            Quaternion.identity,
            this.root.transform
        );

        aoeObj.GetComponent<AoeState>().InitByAoeLauncher(aoeLauncher);
    }

    ///<summary>
    ///删除一个存在的aoeObject
    ///<param name="aoe">aoe的GameObject</param>
    ///<param name="immediately">是否当场清除，如果false，就是把时间变成0</param>
    ///</summary>
    public RemoveAoE(aoe: GameObject, immediately: boolean = false): void {
        if (!aoe) return;
        const aoeState = aoe.GetComponent<AoeState>();
        if (!aoeState) return;
        aoeState.duration = 0;
        if (immediately == true) {
            if (aoeState.model.onRemoved != null) {
                aoeState.model.onRemoved(aoe);
            }
            Destroy(aoe);
        }
    }

    ///<summary>
    ///创建一个视觉特效在场景上
    ///<param name="prefab">特效的prefab文件夹，约定就在Prefabs/下，所以路径不应该加这段</param>
    ///<param name="pos">创建的位置</param>
    ///<param name="degree">角度</param>
    ///<param name="key">特效的key，如果重复则无法创建，删除的时候也有用，空字符串的话不加入管理</param>
    ///<param name="loop">是否循环，循环的得手动remove</param>
    ///</summary>
    public CreateSightEffect(prefab: string, pos: Vector3, degree: number, key: string = "", loop: boolean = false): void {
        if (key in this.sightEffect) return;    //已经存在，加不成

        const effectGO = Instantiate<GameObject>(
            Resources.Load<GameObject>("Prefabs/"+prefab),
            pos,
            Quaternion.identity,
            this.gameObject.transform
        );
        effectGO.transform.RotateAround(effectGO.transform.position, Vector3.up, degree);
        if (!effectGO) return;
        const se = effectGO.GetComponent<SightEffect>();
        if (!se) {
            Destroy(effectGO);
            return;
        }
        if (loop == false) {
            effectGO.AddComponent<UnitRemover>().duration = se.duration;
        }

        if (key != "")  this.sightEffect[key] = effectGO;
    }

    ///<summary>
    ///删除一个视觉特效在场景上
    ///<param name="key">特效的key</param>
    ///</summary>
    public RemoveSightEffect(key: string): void {
        if (!(key in this.sightEffect)) return;
        Destroy(this.sightEffect[key]);
        delete this.sightEffect[key];
    }

    ///<summary>
    ///创建一个角色到场上
    ///<param name="prefab">特效的prefab文件夹，约定就在Prefabs/Character/下，所以路径不应该加这段</param>
    ///<param name="unitAnimInfo">角色的动画信息</param>
    ///<param name="side">所属阵营</param>
    ///<param name="pos">创建的位置</param>
    ///<param name="degree">角度</param>
    ///<param name="baseProp">初期的基础属性</param>
    ///<param name="tags">角色的标签，分类角色用的</param>
    ///</summary>
    public CreateCharacter(prefab: string, side: number, pos: Vector3, baseProp: ChaProperty, degree: number, unitAnimInfo: string = "Default_Gunner", tags: string[] = null): GameObject {
        const chaObj = this.CreateFromPrefab("Character/CharacterObj");
        //Vector3 playerPos = SceneVariants.map.GetRandomPosForCharacter(new RectInt(0, 0, SceneVariants.map.MapWidth(), SceneVariants.map.MapHeight()));
        //cha.AddComponent<PlayerController>().mainCamera = Camera.main; //敌人没有controller
        const cs = chaObj.GetComponent<ChaState>();
        if (cs) {
            cs.InitBaseProp(baseProp);
            cs.side = side;
            let aInfo: { [key: string]: AnimInfo } = {};
            if (unitAnimInfo != "" && unitAnimInfo in DesingerTables.UnitAnimInfo.data) {
                aInfo = DesingerTables.UnitAnimInfo.data[unitAnimInfo];
            }
            cs.SetView(this.CreateFromPrefab("Character/" + prefab), aInfo);
            if (tags != null) cs.tags = tags;
        }

        chaObj.transform.position = pos;
        chaObj.transform.RotateAround(chaObj.transform.position, Vector3.up, degree);
        return chaObj;
    }
}
