///<summary>
///负责aoe的移动、生命周期等
///还负责aoe和角色、子弹的碰撞，需要加aoe碰撞也在这里。值得注意的是：aoe是主体
///aoe捕捉范围与子弹碰撞不同的是，他不判断角色的体型（hitRadius或者bodyRadius），当然如果需要也可以加上，只是这个demo里不需要
///</summary>
export class AoeManager extends MonoBehaviour {
    private FixedUpdate(): void {
        const aoe = GameObject.FindGameObjectsWithTag("AoE");
        if (aoe.length <= 0) return;
        const cha = GameObject.FindGameObjectsWithTag("Character");
        const bullet = GameObject.FindGameObjectsWithTag("Bullet");

        const timePassed = Time.fixedDeltaTime;

        for (let i = 0; i < aoe.length; i++) {
            const aoeState = aoe[i].GetComponent<AoeState>();
            if (!aoeState) continue;

            //首先是aoe的移动
            if (aoeState.duration > 0 && aoeState.tween != null) {
                const aoeMoveInfo = aoeState.tween(aoe[i], aoeState.tweenRunnedTime);
                aoeState.tweenRunnedTime += timePassed;
                aoeState.SetMoveAndRotate(aoeMoveInfo);
            }
            
            if (aoeState.justCreated == true) {
                //刚创建的，走onCreate
                aoeState.justCreated = false;
                //捕获所有角色
                for (let m = 0; m < cha.length; m++) {
                    if (
                        cha[m] &&
                        Utils.InRange(
                            aoe[i].transform.position.x, aoe[i].transform.position.z, 
                            cha[m].transform.position.x, cha[m].transform.position.z,
                            aoeState.radius
                        ) == true
                    ) {
                        aoeState.characterInRange.push(cha[m]);
                    }
                }
                //捕获所有的子弹
                for (let m = 0; m < bullet.length; m++) {
                    if (
                        bullet[m] &&
                        Utils.InRange(
                            aoe[i].transform.position.x, aoe[i].transform.position.z, 
                            bullet[m].transform.position.x, bullet[m].transform.position.z,
                            aoeState.radius
                        ) == true
                    ) {
                        aoeState.bulletInRange.push(bullet[m]);
                    }
                }
                //执行OnCreate
                if (aoeState.model.onCreate != null) {
                    aoeState.model.onCreate(aoe[i]);
                }
                
            } else {
                //已经创建完成的
                //先抓角色离开事件
                const leaveCha: GameObject[] = [];
                let toRemove: GameObject[] = [];
                for (let m = 0; m < aoeState.characterInRange.length; m++) {
                    if (aoeState.characterInRange[m] != null) {
                        if (Utils.InRange(
                                aoe[i].transform.position.x, aoe[i].transform.position.z, 
                                aoeState.characterInRange[m].gameObject.transform.position.x, aoeState.characterInRange[m].gameObject.transform.position.z,
                                aoeState.radius
                            ) == false
                        ) {
                            leaveCha.push(aoeState.characterInRange[m]);
                            toRemove.push(aoeState.characterInRange[m]);
                        }
                    } else {
                        toRemove.push(aoeState.characterInRange[m]);
                    }
                        
                }
                for (let m = 0; m < toRemove.length; m++) {
                    const index = aoeState.characterInRange.indexOf(toRemove[m]);
                    if (index > -1) aoeState.characterInRange.splice(index, 1);
                }
                if (aoeState.model.onChaLeave != null) {
                    aoeState.model.onChaLeave(aoe[i], leaveCha);
                }

                //再看进入的角色
                const enterCha: GameObject[] = [];
                for (let m = 0; m < cha.length; m++) {
                    if (
                        cha[m] &&
                        aoeState.characterInRange.indexOf(cha[m]) < 0 &&
                        Utils.InRange(
                            aoe[i].transform.position.x, aoe[i].transform.position.z, 
                            cha[m].transform.position.x, cha[m].transform.position.z,
                            aoeState.radius
                        ) == true
                    ) {
                        enterCha.push(cha[m]);
                    }
                }
                if (aoeState.model.onChaEnter != null) {
                    aoeState.model.onChaEnter(aoe[i], enterCha);
                }
                for (let m = 0; m < enterCha.length; m++) {
                    if (enterCha[m] != null && enterCha[m].GetComponent<ChaState>() && enterCha[m].GetComponent<ChaState>().dead == false) {
                        aoeState.characterInRange.push(enterCha[m]);
                    }
                }

                //子弹离开
                const leaveBullet: GameObject[] = [];
                toRemove = [];
                for (let m = 0; m < aoeState.bulletInRange.length; m++) {
                    if (aoeState.bulletInRange[m]) {
                        if (Utils.InRange(
                                aoe[i].transform.position.x, aoe[i].transform.position.z,
                                aoeState.bulletInRange[m].gameObject.transform.position.x, aoeState.bulletInRange[m].gameObject.transform.position.z,
                                aoeState.radius
                            ) == false
                        ) {
                            leaveBullet.push(aoeState.bulletInRange[m]);
                            toRemove.push(aoeState.bulletInRange[m]);
                        }
                    } else {
                        toRemove.push(aoeState.bulletInRange[m]);
                    }

                }
                for (let m = 0; m < toRemove.length; m++) {
                    const index = aoeState.bulletInRange.indexOf(toRemove[m]);
                    if (index > -1) aoeState.bulletInRange.splice(index, 1);
                }
                if (aoeState.model.onBulletLeave != null) {
                    aoeState.model.onBulletLeave(aoe[i], leaveBullet);
                }
                toRemove = null;

                //子弹进入
                const enterBullet: GameObject[] = [];
                for (let m = 0; m < bullet.length; m++) {
                    if (
                        bullet[m] &&
                        aoeState.bulletInRange.indexOf(bullet[m]) < 0 &&
                        Utils.InRange(
                            aoe[i].transform.position.x, aoe[i].transform.position.z,
                            bullet[m].transform.position.x, bullet[m].transform.position.z,
                            aoeState.radius
                        ) == true
                    ) {
                        enterBullet.push(bullet[m]);
                    }
                }
                if (aoeState.model.onBulletEnter != null) {
                    aoeState.model.onBulletEnter(aoe[i], enterBullet);
                }
                for (let m = 0; m < enterBullet.length; m++) {
                    if (enterBullet[m] != null) {
                        aoeState.bulletInRange.push(enterBullet[m]);
                    }
                }
            }
            //然后是aoe的duration
            aoeState.duration -= timePassed;
            aoeState.timeElapsed += timePassed;
            if (aoeState.duration <= 0 || aoeState.HitObstacle() == true) {
                if (aoeState.model.onRemoved != null) {
                    aoeState.model.onRemoved(aoe[i]);
                }
                Destroy(aoe[i]);
                continue;
            } else {
                 //最后是onTick，remove
                if (
                    aoeState.model.tickTime > 0 && aoeState.model.onTick != null &&
                    Math.round(aoeState.duration * 1000) % Math.round(aoeState.model.tickTime * 1000) == 0
                ) {
                    aoeState.model.onTick(aoe[i]);
                }
            }


        }


    }
}
