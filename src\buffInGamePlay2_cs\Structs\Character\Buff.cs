///<summary>
///用于添加一条buff的信息
///</summary>
export class AddBuffInfo {
    ///<summary>
    ///buff的负责人是谁，可以是null
    ///</summary>
    public caster: GameObject;

    ///<summary>
    ///buff要添加给谁，这个必须有
    ///</summary>
    public target: GameObject;

    ///<summary>
    ///buff的model，这里当然可以从数据里拿，也可以是逻辑脚本现生成的
    ///</summary>
    public buffModel: BuffModel;

    ///<summary>
    ///要添加的层数，负数则为减少
    ///</summary>
    public addStack: number;

    ///<summary>
    ///关于时间，是改变还是设置为, true代表设置为，false代表改变
    ///</summary>
    public durationSetTo: boolean;

    ///<summary>
    ///是否是一个永久的buff，即便=true，时间设置也是有意义的，因为时间如果被减少到0以下，即使是永久的也会被删除
    ///</summary>
    public permanent: boolean;

    ///<summary>
    ///时间值，设置为这个值，或者加上这个值，单位：秒
    ///</summary>
    public duration: number;

    ///<summary>
    ///buff的一些参数，这些参数是逻辑使用的，比如wow中牧师的盾还能吸收多少伤害，就可以记录在buffParam里面
    ///</summary>
    public buffParam: { [key: string]: any };

    constructor(
        model: BuffModel, caster: GameObject, target: GameObject,
        stack: number, duration: number, durationSetTo: boolean = true,
        permanent: boolean = false,
        buffParam: { [key: string]: any } = null
    ) {
        this.buffModel = model;
        this.caster = caster;
        this.target = target;
        this.addStack = stack;
        this.duration = duration;
        this.durationSetTo = durationSetTo;
        this.buffParam = buffParam;
        this.permanent = permanent;
    }
}


///<summary>
///游戏中运行的、角色身上存在的buff
///</summary>
export class BuffObj {
    ///<summary>
    ///这是个什么buff
    ///</summary>
    public model: BuffModel;

    ///<summary>
    ///剩余多久，单位：秒
    ///</summary>
    public duration: number;

    ///<summary>
    ///是否是一个永久的buff，永久的duration不会减少，但是timeElapsed还会增加
    ///</summary>
    public permanent: boolean;

    ///<summary>
    ///当前层数
    ///</summary>
    public stack: number;

    ///<summary>
    ///buff的施法者是谁，当然可以是空的
    ///</summary>
    public caster: GameObject;

    ///<summary>
    ///buff的携带者，实际上是作为参数传递给脚本用，具体是谁，可定是所在控件的this.gameObject了
    ///</summary>
    public carrier: GameObject;

    ///<summary>
    ///buff已经存在了多少时间了，单位：秒
    ///</summary>
    public timeElapsed: number = 0.00;

    ///<summary>
    ///buff执行了多少次onTick了，如果不会执行onTick，那将永远是0
    ///</summary>
    public ticked: number = 0;

    ///<summary>
    ///buff的一些参数，这些参数是逻辑使用的，比如wow中牧师的盾还能吸收多少伤害，就可以记录在buffParam里面
    ///</summary>
    public buffParam: { [key: string]: any } = {};

    constructor(
        model: BuffModel, caster: GameObject, carrier: GameObject, duration: number, stack: number, permanent: boolean = false,
        buffParam: { [key: string]: any } = null
    ) {
        this.model = model;
        this.caster = caster;
        this.carrier = carrier;
        this.duration = duration;
        this.stack = stack;
        this.permanent = permanent;
        if (buffParam != null) {
            for (const key in buffParam) {
                this.buffParam[key] = buffParam[key];
            }
        }
    }
}

///<summary>
///策划填表的内容
///</summary>
export class BuffModel {
    ///<summary>
    ///buff的id
    ///</summary>
    public id: string;

    ///<summary>
    ///buff的名称
    ///</summary>
    public name: string;

    ///<summary>
    ///buff的优先级，优先级越低的buff越后面执行，这是一个非常重要的属性
    ///比如经典的"吸收50点伤害"和"受到的伤害100%反弹给攻击者"应该反弹多少，取决于这两个buff的priority谁更高
    ///</summary>
    public priority: number;

    ///<summary>
    ///buff堆叠的规则中需要的层数，在这个游戏里只要id和caster相同的buffObj就可以堆叠
    ///激战2里就不同，尽管图标显示堆叠，其实只是统计了有多少个相同id的buffObj作为层数显示了
    ///</summary>
    public maxStack: number;

    ///<summary>
    ///buff的tag
    ///</summary>
    public tags: string[];

    ///<summary>
    ///buff的工作周期，单位：秒。
    ///每多少秒执行工作一次，如果<=0则代表不会周期性工作，只要>0，则最小值为Time.FixedDeltaTime。
    ///</summary>
    public tickTime: number;

    ///<summary>
    ///buff会给角色添加的属性，这些属性根据这个游戏设计只有2种，plus和times，所以这个数组实际上只有2维
    ///</summary>
    public propMod: ChaProperty[];

    ///<summary>
    ///buff对于角色的ChaControlState的影响
    ///</summary>
    public stateMod: ChaControlState;

    ///<summary>
    ///buff在被添加、改变层数时候触发的事件
    ///<param name="buff">会传递给脚本buffObj作为参数</param>
    ///<param name="modifyStack">会传递本次改变的层数</param>
    ///</summary>
    public onOccur: BuffOnOccur;
    public onOccurParams: any[];

    ///<summary>
    ///buff在每个工作周期会执行的函数，如果这个函数为空，或者tickTime<=0，都不会发生周期性工作
    ///<param name="buff">会传递给脚本buffObj作为参数</param>
    ///</summary>
    public onTick: BuffOnTick;
    public onTickParams: any[];

    ///<summary>
    ///在这个buffObj被移除之前要做的事情，如果运行之后buffObj又不足以被删除了就会被保留
    ///<param name="buff">会传递给脚本buffObj作为参数</param>
    ///</summary>
    public onRemoved: BuffOnRemoved;
    public onRemovedParams: any[];

    ///<summary>
    ///在释放技能的时候运行的buff，执行这个buff获得最终技能要产生的Timeline
    ///<param name="buff">会传递给脚本的buffObj</param>
    ///<param name="skill">即将释放的技能skillObj</param>
    ///<param name="timeline">释放出来的技能，也就是一个timeline，这里的本质就是让你通过buff还能对timeline进行hack以达到修改技能效果的目的</return>
    ///</summary>
    public onCast: BuffOnCast;
    public onCastParams: any[];

    ///<summary>
    ///在伤害流程中，持有这个buff的人作为攻击者会发生的事情
    ///<param name="buff">会传递给脚本buffObj作为参数</param>
    ///<param name="damageInfo">这次的伤害信息</param>
    ///<param name="target">挨打的角色对象</param>
    ///</summary>
    public onHit: BuffOnHit;
    public onHitParams: any[];

    ///<summary>
    ///在伤害流程中，持有这个buff的人作为挨打者会发生的事情
    ///<param name="buff">会传递给脚本buffObj作为参数</param>
    ///<param name="damageInfo">这次的伤害信息</param>
    ///<param name="attacker">打我的角色，当然可以是空的</param>
    ///</summary>
    public onBeHurt: BuffOnBeHurt;
    public onBeHurtParams: any[];

    ///<summary>
    ///在伤害流程中，如果击杀目标，则会触发的啥事情
    ///<param name="buff">会传递给脚本buffObj作为参数</param>
    ///<param name="damageInfo">这次的伤害信息</param>
    ///<param name="target">挨打的角色对象</param>
    ///</summary>
    public onKill: BuffOnKill;
    public onKillParams: any[];

    ///<summary>
    ///在伤害流程中，持有这个buff的人被杀死了，会触发的事情
    ///<param name="buff">会传递给脚本buffObj作为参数</param>
    ///<param name="damageInfo">这次的伤害信息</param>
    ///<param name="attacker">发起攻击造成击杀的角色对象</param>
    ///</summary>
    public onBeKilled: BuffOnBeKilled;
    public onBeKilledParams: any[];

    constructor(
        id: string, name: string, tags: string[], priority: number, maxStack: number, tickTime: number,
        onOccur: string, occurParam: any[],
        onRemoved: string, removedParam: any[],
        onTick: string, tickParam: any[],
        onCast: string, castParam: any[],
        onHit: string, hitParam: any[],
        beHurt: string, hurtParam: any[],
        onKill: string, killParam: any[],
        beKilled: string, beKilledParam: any[],
        stateMod: ChaControlState, propMod: ChaProperty[] = null
    ) {
        this.id = id;
        this.name = name;
        this.tags = tags;
        this.priority = priority;
        this.maxStack = maxStack;
        this.stateMod = stateMod;
        this.tickTime = tickTime;

        this.propMod = [
            ChaProperty.zero,
            ChaProperty.zero
        ];
        if (propMod != null) {
            for (let i = 0; i < Math.min(2, propMod.length); i++) {
                this.propMod[i] = propMod[i];
            }
        }

        this.onOccur = (onOccur == "") ? null : DesignerScripts.Buff.onOccurFunc[onOccur];
        this.onOccurParams = occurParam;
        this.onRemoved = (onRemoved == "") ? null : DesignerScripts.Buff.onRemovedFunc[onRemoved];
        this.onRemovedParams = removedParam;
        this.onTick = (onTick == "") ? null : DesignerScripts.Buff.onTickFunc[onTick];
        this.onTickParams = tickParam;
        this.onCast = (onCast == "") ? null : DesignerScripts.Buff.onCastFunc[onCast];
        this.onCastParams = castParam;
        this.onHit = (onHit == "") ? null : DesignerScripts.Buff.onHitFunc[onHit];
        this.onHitParams = hitParam;
        this.onBeHurt = (beHurt == "") ? null: DesignerScripts.Buff.beHurtFunc[beHurt];
        this.onBeHurtParams = hurtParam;
        this.onKill = (onKill == "") ? null : DesignerScripts.Buff.onKillFunc[onKill];
        this.onKillParams = killParam;
        this.onBeKilled = (beKilled == "") ? null : DesignerScripts.Buff.beKilledFunc[beKilled];
        this.onBeKilledParams = beKilledParam;
    }
}

export type BuffOnOccur = (buff: BuffObj, modifyStack: number) => void;
export type BuffOnRemoved = (buff: BuffObj) => void;
export type BuffOnTick = (buff: BuffObj) => void;
export type BuffOnHit = (buff: BuffObj, damageInfo: DamageInfo, target: GameObject) => void;
export type BuffOnBeHurt = (buff: BuffObj, damageInfo: DamageInfo, attacker: GameObject) => void;
export type BuffOnKill = (buff: BuffObj, damageInfo: DamageInfo, target: GameObject) => void;
export type BuffOnBeKilled = (buff: BuffObj, damageInfo: DamageInfo, attacker: GameObject) => void;
export type BuffOnCast = (buff: BuffObj, skill: SkillObj, timeline: TimelineObj) => TimelineObj;
