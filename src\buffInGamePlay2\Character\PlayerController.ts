///<summary>
///玩家操作的控件，理论上它只能被加在"主角身上"。
///但如果我们有类似wow牧师的精神控制之类的技能、或者是控制多个分身同步行动的，就需要给多目标添加了
///</summary>
export class PlayerController extends MonoBehaviour {
    public mainCamera: Camera;

    private chaState: ChaState;

    Start(): void {
        this.chaState = this.gameObject.GetComponent<ChaState>();   
    }

    FixedUpdate(): void {
        if (!this.chaState || this.chaState.dead == true) return;

        const ix = Input.GetAxis("Horizontal");
        const iz = Input.GetAxis("Vertical");
        const sBtn: boolean[] = [
            Input.GetButton("Fire5"),
            Input.GetButton("Fire4"),
            Input.GetButton("Fire3"),
            Input.GetButton("Fire2"),
            Input.GetButton("Fire1"),
            Input.GetButton("Jump")
        ];
        
        const cursorPos = Input.mousePosition;

        let rotateTo = this.transform.rotation.eulerAngles.y;
        //TODO，这里不应该直接给UnitMove UnitRotate发信息
        if (this.mainCamera) {
            //先获得主角的屏幕坐标，然后对比鼠标坐标就知道转向了
            const mScreenPos = RectTransformUtility.WorldToScreenPoint(this.mainCamera, this.transform.position);
            rotateTo = Math.atan2(cursorPos.x - mScreenPos.x, cursorPos.y - mScreenPos.y) * 180.00 / Math.PI;
            this.chaState.OrderRotateTo(rotateTo);
        }

        if (ix != 0 || iz != 0) {
            const mSpd = this.chaState.moveSpeed;
            const mInfo = new Vector3(ix*mSpd, 0, iz*mSpd);
            this.chaState.OrderMove(mInfo);
        }

        const skillId: string[] = [
             "explosiveBarrel", "teleportBullet","grenade","cloakBoomerang","fire","roll"
        ];

        let btnHolding = false;
        for (let i = 0; i < sBtn.length; i++) {
            if (sBtn[i] == true) {
                this.chaState.CastSkill(skillId[i]);
                btnHolding = true;
            }
        }
        this.chaState.charging = btnHolding;
    }
}
