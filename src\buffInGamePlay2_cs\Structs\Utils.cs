export class Utils {

    public static CircleHitRects(circlePivot: Vector2, circleRadius: number, rects: Rect[]): boolean {
        if (rects.length <= 0) return false;
        for (let i = 0; i < rects.length; i++) {
            if (Utils.CircleHitRect(circlePivot, circleRadius, rects[i]) == true) {
                return true;
            }
        }
        return false;
    }

    public static CircleHitRect(circlePivot: Vector2, circleRadius: number, rect: Rect): boolean {
        const xp = circlePivot.x < rect.x ? 0 : (circlePivot.x > rect.x + rect.width ? 2 : 1);
        const yp = circlePivot.y < rect.y ? 0 : (circlePivot.y > rect.y + rect.height ? 2 : 1);
        
        if (yp == 1 && xp == 1) return true;  //在中间，则一定命中
        
        if (yp != 1 && xp == 1) {
            const halfRect = rect.height / 2;
            const toHeart = Math.abs(circlePivot.y - (rect.y + halfRect));
            return (toHeart <= circleRadius + halfRect);
        } else
        if (yp == 1 && xp != 1) {
            const halfRect = rect.width / 2;
            const toHeart = Math.abs(circlePivot.x - (rect.x + halfRect));
            return (toHeart <= circleRadius + halfRect);
        } else {
            return Utils.InRange(
                circlePivot.x, circlePivot.y, 
                yp == 0 ? rect.x : (rect.x + rect.width), 
                xp == 0 ? rect.y : (rect.y + rect.height), 
                circleRadius
            );
        }
    }

    ///<summary>
    ///AABB的矩形之间是否有碰撞
    ///<param name="a">一个rect</param>
    ///<param name="b">另一个rect</param>
    ///<return>是否碰撞到了，true代表碰到了</return>
    ///</summary>
    public static RectCollide(a: Rect, b: Rect): boolean {
        const ar = a.x + a.width;
        const br = b.x + b.width;
        const ab = a.y + a.height;
        const bb = b.y + b.height;
        return (
            (a.x >= b.x && a.x <= br) ||
            (b.x >= a.x && b.x <= ar)
        ) && (
            (a.y >= b.y && a.y <= bb) ||
            (b.y >= a.y && b.y <= ab)
        );
    }

   
    public static InRange(x1: number, y1: number, x2: number, y2: number, range: number): boolean {
        return Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2) <= Math.pow(range, 2);
    }


    ///<summary>
    ///根据面向和移动方向得到一个资源名预订了规则的后缀名
    ///<param name="faceDegree">面向角度</param>
    ///<param name="moveDegree">移动角度</param>
    ///<return>约定好的关键字，比如"Forward","Back","Left","Right"，对应到角色动画的key</return>
    ///</summary>
    public static GetTailStringByDegree(faceDegree: number, moveDegree: number): string {
        let fd = faceDegree;
        let md = moveDegree;
        while (fd < 180) fd += 360;
        while (md < 180) md += 360;
        fd = fd % 360;
        md = md % 360;
        let dd = md - fd;
        if (dd > 180) {
            dd -= 360;
        } else if (dd < -180) {
            dd += 360;
        }
        //Debug.Log("degree:"+fd + " / " + md + " / " + dd);
        if (dd >= -45 && dd <= 45) {
            return "Forward";
        } else
        if (dd < -45 && dd >= -135) {
            return "Left";
        } else
        if (dd > 45 && dd <= 135) {
            return "Right";
        } else {
            return "Back";
        }
    }

}
