///<summary>
///管理角色脚底下那个血条的
///</summary>
export class ChaPie extends MonoBehaviour {
    private chaState: ChaState;
    private chart: <PERSON><PERSON>hartController;

    private Start(): void {
        this.chaState = this.gameObject.GetComponent<ChaState>();
        this.chart = this.gameObject.GetComponentInChildren<PieChartController>();

        if (!this.chaState || !this.chart) return;
        this.chart.radius = this.chaState.property.bodyRadius;
    }

    private FixedUpdate(): void {
        if (!this.chaState || !this.chart) return;

        this.chart.angleDegree = 360 * this.chaState.resource.hp / this.chaState.property.hp;
        this.chart.transform.localEulerAngles = new Vector3(
            this.chart.transform.localRotation.eulerAngles.x,
            -this.transform.eulerAngles.y,
            this.chart.transform.localRotation.eulerAngles.z
        );
    }
}
