import { GameObject } from 'UnityEngine';
import { DamageInfo, TimelineObj, SkillObj, ChaProperty, ChaControlState } from '../other-types';

/**
 * 用于添加一条buff的信息
 */
class AddBuffInfo {
    /**
     * buff的负责人是谁，可以是null
     */
    caster: GameObject | null;

    /**
     * buff要添加给谁，这个必须有
     */
    target: GameObject;

    /**
     * buff的model，这里当然可以从数据里拿，也可以是逻辑脚本现生成的
     */
    buffModel: BuffModel;

    /**
     * 要添加的层数，负数则为减少
     */
    addStack: number;

    /**
     * 关于时间，是改变还是设置为, true代表设置为，false代表改变
     */
    durationSetTo: boolean;

    /**
     * 是否是一个永久的buff，即便=true，时间设置也是有意义的，因为时间如果被减少到0以下，即使是永久的也会被删除
     */
    permanent: boolean;

    /**
     * 时间值，设置为这个值，或者加上这个值，单位：秒
     */
    duration: number;

    /**
     * buff的一些参数，这些参数是逻辑使用的，比如wow中牧师的盾还能吸收多少伤害，就可以记录在buffParam里面
     */
    buffParam: { [key: string]: any } | null;

    constructor(
        model: BuffModel,
        caster: GameObject | null,
        target: GameObject,
        stack: number,
        duration: number,
        durationSetTo: boolean = true,
        permanent: boolean = false,
        buffParam: { [key: string]: any } | null = null
    ) {
        this.buffModel = model;
        this.caster = caster;
        this.target = target;
        this.addStack = stack;
        this.duration = duration;
        this.durationSetTo = durationSetTo;
        this.buffParam = buffParam;
        this.permanent = permanent;
    }
}

/**
 * 游戏中运行的、角色身上存在的buff
 */
class BuffObj {
    /**
     * 这是个什么buff
     */
    model: BuffModel;

    /**
     * 剩余多久，单位：秒
     */
    duration: number;

    /**
     * 是否是一个永久的buff，永久的duration不会减少，但是timeElapsed还会增加
     */
    permanent: boolean;

    /**
     * 当前层数
     */
    stack: number;

    /**
     * buff的施法者是谁，当然可以是空的
     */
    caster: GameObject | null;

    /**
     * buff的携带者，实际上是作为参数传递给脚本用，具体是谁，可定是所在控件的this.gameObject了
     */
    carrier: GameObject;

    /**
     * buff已经存在了多少时间了，单位：秒
     */
    timeElapsed: number = 0.00;

    /**
     * buff执行了多少次onTick了，如果不会执行onTick，那将永远是0
     */
    ticked: number = 0;

    /**
     * buff的一些参数，这些参数是逻辑使用的，比如wow中牧师的盾还能吸收多少伤害，就可以记录在buffParam里面
     */
    buffParam: { [key: string]: any } = {};

    constructor(
        model: BuffModel,
        caster: GameObject | null,
        carrier: GameObject,
        duration: number,
        stack: number,
        permanent: boolean = false,
        buffParam: { [key: string]: any } | null = null
    ) {
        this.model = model;
        this.caster = caster;
        this.carrier = carrier;
        this.duration = duration;
        this.stack = stack;
        this.permanent = permanent;
        if (buffParam != null) {
            for (const key in buffParam) {
                if (buffParam.hasOwnProperty(key)) {
                    this.buffParam[key] = buffParam[key];
                }
            }
        }
    }
}

/**
 * 策划填表的内容
 */
class BuffModel {
    /**
     * buff的id
     */
    id: string;

    /**
     * buff的名称
     */
    name: string;

    /**
     * buff的优先级，优先级越低的buff越后面执行，这是一个非常重要的属性
     * 比如经典的“吸收50点伤害”和“受到的伤害100%反弹给攻击者”应该反弹多少，取决于这两个buff的priority谁更高
     */
    priority: number;

    /**
     * buff堆叠的规则中需要的层数，在这个游戏里只要id和caster相同的buffObj就可以堆叠
     * 激战2里就不同，尽管图标显示堆叠，其实只是统计了有多少个相同id的buffObj作为层数显示了
     */
    maxStack: number;

    /**
     * buff的tag
     */
    tags: string[];

    /**
     * buff的工作周期，单位：秒。
     * 每多少秒执行工作一次，如果<=0则代表不会周期性工作，只要>0，则最小值为Time.FixedDeltaTime。
     */
    tickTime: number;

    /**
     * buff会给角色添加的属性，这些属性根据这个游戏设计只有2种，plus和times，所以这个数组实际上只有2维
     */
    propMod: ChaProperty[];

    /**
     * buff对于角色的ChaControlState的影响
     */
    stateMod: ChaControlState;

    /**
     * buff在被添加、改变层数时候触发的事件
     * @param buff 会传递给脚本buffObj作为参数
     * @param modifyStack 会传递本次改变的层数
     */
    onOccur: BuffOnOccur | null;
    onOccurParams: any[] | null;

    /**
     * buff在每个工作周期会执行的函数，如果这个函数为空，或者tickTime<=0，都不会发生周期性工作
     * @param buff 会传递给脚本buffObj作为参数
     */
    onTick: BuffOnTick | null;
    onTickParams: any[] | null;

    /**
     * 在这个buffObj被移除之前要做的事情，如果运行之后buffObj又不足以被删除了就会被保留
     * @param buff 会传递给脚本buffObj作为参数
     */
    onRemoved: BuffOnRemoved | null;
    onRemovedParams: any[] | null;

    /**
     * 在释放技能的时候运行的buff，执行这个buff获得最终技能要产生的Timeline
     * @param buff 会传递给脚本的buffObj
     * @param skill 即将释放的技能skillObj
     * @param timeline 释放出来的技能，也就是一个timeline，这里的本质就是让你通过buff还能对timeline进行hack以达到修改技能效果的目的
     */
    onCast: BuffOnCast | null;
    onCastParams: any[] | null;

    /**
     * 在伤害流程中，持有这个buff的人作为攻击者会发生的事情
     * @param buff 会传递给脚本buffObj作为参数
     * @param damageInfo 这次的伤害信息
     * @param target 挨打的角色对象
     */
    onHit: BuffOnHit | null;
    onHitParams: any[] | null;

    /**
     * 在伤害流程中，持有这个buff的人作为挨打者会发生的事情
     * @param buff 会传递给脚本buffObj作为参数
     * @param damageInfo 这次的伤害信息
     * @param attacker 打我的角色，当然可以是空的
     */
    onBeHurt: BuffOnBeHurt | null;
    onBeHurtParams: any[] | null;

    /**
     * 在伤害流程中，如果击杀目标，则会触发的啥事情
     * @param buff 会传递给脚本buffObj作为参数
     * @param damageInfo 这次的伤害信息
     * @param target 挨打的角色对象
     */
    onKill: BuffOnKill | null;
    onKillParams: any[] | null;

    /**
     * 在伤害流程中，持有这个buff的人被杀死了，会触发的事情
     * @param buff 会传递给脚本buffObj作为参数
     * @param damageInfo 这次的伤害信息
     * @param attacker 发起攻击造成击杀的角色对象
     */
    onBeKilled: BuffOnBeKilled | null;
    onBeKilledParams: any[] | null;

    constructor(
        id: string,
        name: string,
        tags: string[],
        priority: number,
        maxStack: number,
        tickTime: number,
        onOccur: string,
        occurParam: any[] | null,
        onRemoved: string,
        removedParam: any[] | null,
        onTick: string,
        tickParam: any[] | null,
        onCast: string,
        castParam: any[] | null,
        onHit: string,
        hitParam: any[] | null,
        beHurt: string,
        hurtParam: any[] | null,
        onKill: string,
        killParam: any[] | null,
        beKilled: string,
        beKilledParam: any[] | null,
        stateMod: ChaControlState,
        propMod: ChaProperty[] | null = null
    ) {
        this.id = id;
        this.name = name;
        this.tags = tags;
        this.priority = priority;
        this.maxStack = maxStack;
        this.stateMod = stateMod;
        this.tickTime = tickTime;

        this.propMod = [ChaProperty.zero, ChaProperty.zero];
        if (propMod != null) {
            for (let i = 0; i < Math.min(2, propMod.length); i++) {
                this.propMod[i] = propMod[i];
            }
        }

        // Assuming these functions are registered elsewhere
        this.onOccur = (onOccur === "") ? null : (DesignerScripts.Buff.onOccurFunc as any)[onOccur];
        this.onOccurParams = occurParam;
        this.onRemoved = (onRemoved === "") ? null : (DesignerScripts.Buff.onRemovedFunc as any)[onRemoved];
        this.onRemovedParams = removedParam;
        this.onTick = (onTick === "") ? null : (DesignerScripts.Buff.onTickFunc as any)[onTick];
        this.onTickParams = tickParam;
        this.onCast = (onCast === "") ? null : (DesignerScripts.Buff.onCastFunc as any)[onCast];
        this.onCastParams = castParam;
        this.onHit = (onHit === "") ? null : (DesignerScripts.Buff.onHitFunc as any)[onHit];
        this.onHitParams = hitParam;
        this.onBeHurt = (beHurt === "") ? null : (DesignerScripts.Buff.beHurtFunc as any)[beHurt];
        this.onBeHurtParams = hurtParam;
        this.onKill = (onKill === "") ? null : (DesignerScripts.Buff.onKillFunc as any)[onKill];
        this.onKillParams = killParam;
        this.onBeKilled = (beKilled === "") ? null : (DesignerScripts.Buff.beKilledFunc as any)[beKilled];
        this.onBeKilledParams = beKilledParam;
    }
}

type BuffOnOccur = (buff: BuffObj, modifyStack: number) => void;
type BuffOnRemoved = (buff: BuffObj) => void;
type BuffOnTick = (buff: BuffObj) => void;
type BuffOnHit = (buff: BuffObj, damageInfo: DamageInfo, target: GameObject) => void;
type BuffOnBeHurt = (buff: BuffObj, damageInfo: DamageInfo, attacker: GameObject | null) => void;
type BuffOnKill = (buff: BuffObj, damageInfo: DamageInfo, target: GameObject) => void;
type BuffOnBeKilled = (buff: BuffObj, damageInfo: DamageInfo, attacker: GameObject | null) => void;
type BuffOnCast = (buff: BuffObj, skill: SkillObj, timeline: TimelineObj) => TimelineObj;

// Assuming DesignerScripts is declared elsewhere
declare namespace DesignerScripts {
    namespace Buff {
        const onOccurFunc: { [key: string]: BuffOnOccur };
        const onRemovedFunc: { [key: string]: BuffOnRemoved };
        const onTickFunc: { [key: string]: BuffOnTick };
        const onCastFunc: { [key: string]: BuffOnCast };
        const onHitFunc: { [key: string]: BuffOnHit };
        const beHurtFunc: { [key: string]: BuffOnBeHurt };
        const onKillFunc: { [key: string]: BuffOnKill };
        const beKilledFunc: { [key: string]: BuffOnBeKilled };
    }
}