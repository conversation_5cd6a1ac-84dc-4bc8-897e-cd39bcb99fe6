///<summary>
///aoe的效果
///</summary>
export class AoE {
    public static onCreateFunc: { [key: string]: AoeOnCreate } = {
        "CreateSightEffect": AoE.CreateSightEffect
    };
    public static onRemovedFunc: { [key: string]: AoeOnRemoved } = {
        "DoDamageOnRemoved": AoE.DoDamageOnRemoved,
        "CreateAoeOnRemoved": AoE.CreateAoeOnRemoved,
        "BarrelExplosed": AoE.BarrelExplosed
    };
    public static onTickFunc: { [key: string]: AoeOnTick } = {
        "BlackHole": AoE.BlackHole
    };
    public static onChaEnterFunc: { [key: string]: AoeOnCharacterEnter } = {
        "DoDamageToEnterCha": AoE.DoDamageToEnterCha
    };
    public static onChaLeaveFunc: { [key: string]: AoeOnCharacterLeave } = {
        
    };
    public static onBulletEnterFunc: { [key: string]: AoeOnBulletEnter } = {
        "BlockBullets": AoE.BlockBullets,
        "SpaceMonkeyBallHit": AoE.SpaceMonkeyBallHit
    };
    public static onBulletLeaveFunc: { [key: string]: AoeOnBulletLeave } = {
        
    };
    public static aoeTweenFunc: { [key: string]: AoeTween } = {
        "AroundCaster": AoE.AroundCaster,
        "SpaceMonkeyBallRolling": AoE.SpaceMonkeyBallRolling
    };


    ///<summary>
    ///aoeTween
    ///环绕施法者旋转，参数：
    ///[0]float：距离caster的距离（单位米）
    ///[1]float：移动速度（度/秒），正负的效果是方向
    ///</summary>
    private static AroundCaster(aoe: GameObject, t: number): AoeMoveInfo {
        const aoeState = aoe.GetComponent<AoeState>();
        if (aoeState == null || aoeState.caster == null) return null;
        const b = aoeState.caster.transform.position;
