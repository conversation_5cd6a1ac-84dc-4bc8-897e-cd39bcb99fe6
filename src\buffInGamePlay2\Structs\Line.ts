export class Line {
    public point: Vector2[];

    constructor(point: Vector2[] = null) {
        this.point = [Vector2.zero, Vector2.zero];
        if (point != null) {
            if (point.length > 0) this.point[0] = point[0];
            if (point.length > 1) this.point[1] = point[1];
        }
    }

    ///<summary>
    ///左侧的点
    ///</summary>
    public LeftPoint(): Vector2 {
        return this.point[0].x <= this.point[1].x ? this.point[0] : this.point[1];
    }

    ///<summary>
    ///右侧的点
    ///</summary>
    public RightPoint(): Vector2 {
        return this.point[0].x > this.point[1].x ? this.point[0] : this.point[1];
    }

    ///<summary>
    ///Y值更小的点，但是unity和正常游戏是反的，所以起名难，只能这么来了，因为思维里还是y小在上
    ///</summary>
    public TopPoint(): Vector2 {
        return this.point[0].y <= this.point[1].y ? this.point[0] : this.point[1];
    }

    ///<summary>
    ///Y值更大的点，但是unity和正常游戏是反的，所以起名难，只能这么来了，因为思维里还是y大在下
    ///</summary>
    public BottomPoint(): Vector2 {
        return this.point[0].y > this.point[1].y ? this.point[0] : this.point[1];
    }

    ///<summary>
    ///是否与另外一条线段（Line）相交
    ///</summary>
    public Cross(other: Line): boolean {
        return (
            Math.min(this.point[0].x, this.point[1].x) <= Math.max(other.point[0].x, other.point[1].x) &&
            Math.max(this.point[0].x, this.point[1].x) >= Math.min(other.point[0].x, other.point[1].x) &&
            Math.min(this.point[0].y, this.point[1].y) <= Math.max(other.point[0].y, other.point[1].y) &&
            Math.max(this.point[0].y, this.point[1].y) >= Math.min(other.point[0].y, other.point[1].y) &&
            ((Line.Mul(other.point[0], this.point[0], other.point[1]))*(Line.Mul(other.point[0], other.point[1], this.point[1]))) >= 0 &&
            ((Line.Mul(this.point[0], other.point[0], this.point[1]))*(Line.Mul(this.point[0], this.point[1], other.point[1]))) >= 0
        );
    }

    private static Mul(a: Vector2, b: Vector2, c: Vector2): number {
        return (b.x - a.x)*(c.y - a.y) - (c.x - a.x) * (b.y - a.y);
    }
}
