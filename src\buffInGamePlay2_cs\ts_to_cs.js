const fs = require('fs');
const path = require('path');

/**
 * 递归遍历目录，将 .ts 文件后缀改为 .cs，若存在同名 .cs 文件则先删除
 * @param {string} dir - 要遍历的目录路径
 */
function traverseDirectory(dir) {
    try {
        // 读取目录下的所有文件和文件夹
        const files = fs.readdirSync(dir);
        files.forEach(file => {
            const filePath = path.join(dir, file);
            const stats = fs.statSync(filePath);
            if (stats.isDirectory()) {
                // 如果是目录，递归调用 traverseDirectory 函数
                traverseDirectory(filePath);
            } else if (path.extname(file) === '.ts') {
                // 构造新的 .cs 文件路径
                const newFileName = path.basename(file, '.ts') + '.cs';
                const newFilePath = path.join(dir, newFileName);
                if (fs.existsSync(newFilePath)) {
                    // 若同名 .cs 文件存在，先删除
                    fs.unlinkSync(newFilePath);
                    console.log(`Deleted existing file ${newFilePath}`);
                }
                // 将 .ts 文件重命名为 .cs 文件
                fs.renameSync(filePath, newFilePath);
                console.log(`Renamed ${filePath} to ${newFilePath}`);
            }
        });
    } catch (error) {
        console.error(`Error traversing directory ${dir}:`, error);
    }
}

// 从当前目录开始遍历
traverseDirectory('.');