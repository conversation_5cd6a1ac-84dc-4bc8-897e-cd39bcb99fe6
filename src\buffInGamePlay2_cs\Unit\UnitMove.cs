///<summary>
///单位移动控件，所有需要移动的单位都应该添加这个来控制它的移动，不论是角色，aoe还是子弹，但是地形不能用这个，因为地形是terrain不是unit
///这里负责的是每一帧往一个方向移动，至于往什么方向移动，这应该是其他控件的事情，比如角色是由操作来决定的，子弹则是轨迹决定的
///在这游戏里，角色只有x,z方向移动，依赖于地形的y移动如果有，也不归这个逻辑来管理，而是由视觉元素（比如小土坡）自身决定的
///</summary>
export class UnitMove extends MonoBehaviour {
    //是否有权移动
    private canMove: boolean = true;

    //[Tooltip("单位的移动类型，根据游戏设计不同，这个值也可以不同")]
    public moveType: MoveType = MoveType.ground;

    //[Tooltip("单位的移动体型碰撞圆形的半径，单位：米")]
    public bodyRadius: number = 0.25;

    //[Tooltip(
    //    "当单位移动被地图阻挡的时候，是选择一个更好的落脚点（true）还是直接停止移动（false），如果直接停止移动，那么停下的时候访问hitObstacle的时候就是true，否则hitObstacle永远是false"
    //)]
    public smoothMove: boolean = true;

    //[Tooltip("是否会忽略关卡外围，即飞行（只有飞行允许）到地图外的地方全部视作可过")]
    public ignoreBorder: boolean = true;

    public get hitObstacle(): boolean {
        return this._hitObstacle;
    }
    private _hitObstacle: boolean = false;
    
    //要移动的方向的力，单位：米/秒。
    private velocity: Vector3 = Vector3.zero;

    FixedUpdate(): void {
        if (this.canMove == false || this.velocity == Vector3.zero) return;   
        
        const targetPos = new Vector3(
            this.velocity.x * Time.fixedDeltaTime + this.transform.position.x,
            this.velocity.y * Time.fixedDeltaTime + this.transform.position.y, 
            this.velocity.z * Time.fixedDeltaTime + this.transform.position.z
        );

        const mapTargetPosInfo = SceneVariants.map.FixTargetPosition(
            this.transform.position, this.bodyRadius, targetPos, this.moveType, (this.ignoreBorder == true && this.moveType == MoveType.fly)
        );
        if (this.smoothMove == false && mapTargetPosInfo.obstacle == true) {
            this._hitObstacle = true;
            this.canMove = false;
        }
        this.transform.position = mapTargetPosInfo.suggestPos;

        this.velocity = Vector3.zero;
    }

    

    private StopMoving(): void {
        this.velocity = Vector3.zero;
    }


    ///<summary>
    ///当前的移动方向
    ///</summary>
    public GetMoveDirection(): Vector3 {
        return this.velocity;
    }

    ///<summary>
    ///移动向某个方向，距离也决定了速度，距离单位是米，1秒内移动的量
    ///<param name="moveForce">移动方向和力，单位：米/秒</param>
    ///</summary>
    public MoveBy(moveForce: Vector3): void {
        if (this.canMove == false) return;

        this.velocity = moveForce;
    }

    ///<summary>
    ///禁止角色可以移动能力，会停止当前的移动
    ///终止当前移动看起来是一个side-effect，但是依照游戏规则设计来说，他只是"配套功能"所以严格的说并不是side-effect
    ///</summary>
    public DisableMove(): void {
        this.StopMoving();
        this.canMove = false;
    }

    ///<summary>
    ///开启角色可以移动的能力
    ///</summary>
    public EnableRotate(): void {
        this.canMove = true;
    }
}
