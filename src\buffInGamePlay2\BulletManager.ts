///<summary>
///负责子弹的一切，包括移动、生命周期等
///还负责子弹和角色等的碰撞，需要加入子弹与子弹碰撞也在这里。值得注意的是：子弹是主体
///</summary>
export class BulletManager extends MonoBehaviour {
    private FixedUpdate(): void {
        
        const bullet = GameObject.FindGameObjectsWithTag("Bullet");
        if (bullet.length <= 0) return;
        const character = GameObject.FindGameObjectsWithTag("Character");
        if (bullet.length <= 0 || character.length <= 0) return;

        const timePassed = Time.fixedDeltaTime;

        for (let i = 0; i < bullet.length; i++) {
            const bs = bullet[i].GetComponent<BulletState>();
            if (!bs || bs.hp <= 0) continue;

            //如果是刚创建的，那么就要处理刚创建的事情
            if (bs.timeElapsed <= 0 && bs.model.onCreate != null) {
                bs.model.onCreate(bullet[i]);
            }

            //处理子弹命中纪录信息
            let hIndex = 0;
            while (hIndex < bs.hitRecords.length) {
                bs.hitRecords[hIndex].timeToCanHit -= timePassed;
                if (bs.hitRecords[hIndex].timeToCanHit <= 0 || bs.hitRecords[hIndex].target == null) {
                    //理论上应该支持可以鞭尸，所以即使target dead了也得留着……
                    bs.hitRecords.splice(hIndex, 1);
                } else {
                    hIndex += 1;
                }
            }

            //处理子弹的移动信息
            bs.SetMoveForce(
                bs.tween == null ? Vector3.forward : bs.tween(bs.timeElapsed, bullet[i], bs.followingTarget)
            );

            //处理子弹的碰撞信息，如果子弹可以碰撞，才会执行碰撞逻辑
            if (bs.canHitAfterCreated > 0) {
                bs.canHitAfterCreated -= timePassed;  
            } else {
                const bRadius = bs.model.radius;
                let bSide = -1;
                if (bs.caster) {
                    const bcs = bs.caster.GetComponent<ChaState>();
                    if (bcs) {
                        bSide = bcs.side;
                    }
                }

                for (let j = 0; j < character.length; j++) {
                    if (bs.CanHit(character[j]) == false) continue;

                    const cs = character[j].GetComponent<ChaState>();
                    if (!cs || cs.dead == true || cs.immuneTime > 0) continue;

                    if (
                        (bs.model.hitAlly == false && bSide == cs.side) ||
                        (bs.model.hitFoe == false && bSide != cs.side)
                    ) continue;
                    
                    const cRadius = cs.property.hitRadius;
                    const dis = Vector3.subtract(bullet[i].transform.position, character[j].transform.position);
                    
                    if (Math.pow(dis.x, 2) + Math.pow(dis.z, 2) <= Math.pow(bRadius + cRadius, 2)) {
                        //命中了
                        bs.hp -= 1;

                        if (bs.model.onHit != null) {
                            bs.model.onHit(bullet[i], character[j]);
                        }
                        
                        if (bs.hp > 0) {
                            bs.AddHitRecord(character[j]);
                        } else {
                            Destroy(bullet[i]);
                            continue;
                        }
                    }
                }
            }

            ///生命周期的结算
            bs.duration -= timePassed;
            bs.timeElapsed += timePassed;
            if (bs.duration <= 0 || bs.HitObstacle() == true) {
                if (bs.model.onRemoved != null) {
                    bs.model.onRemoved(bullet[i]);
                }
                Destroy(bullet[i]);
                continue;
            }
        }
    }
}
