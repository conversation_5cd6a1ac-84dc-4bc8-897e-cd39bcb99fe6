///<summary>
///子弹的"状态"，用来管理当前应该怎么移动、应该怎么旋转、应该怎么播放动画的。
///是一个角色的总的"调控中心"。
///</summary>
export class BulletState extends MonoBehaviour {
    ///<summary>
    ///这是一颗怎样的子弹
    ///</summary>
    public model: BulletModel;
    
    ///<summary>
    ///要发射子弹的这个人的gameObject，这里就认角色（拥有ChaState的）
    ///当然可以是null发射的，但是写效果逻辑的时候得小心caster是null的情况
    ///</summary>
    public caster: GameObject;

    ///<summary>
    ///子弹发射时候，caster的属性，如果caster不存在，就会是一个ChaProperty.zero
    ///在一些设计中，比如wow的技能中，技能效果是跟发出时候的角色状态有关的，之后即使获得或者取消了buff，更换了装备，数值一样不会受到影响，所以得记录这个释放当时的值
    ///</summary>
    public propWhileCast: ChaProperty = ChaProperty.zero;

    ///<summary>
    ///发射的角度，单位：角度，如果useFireDegreeForever == true，那就得用这个角度来获得当前飞行路线了
    ///</summary>
    public fireDegree: number;

    ///<summary>
    ///子弹的初速度，单位：米/秒，跟Tween结合获得Tween得到当前移动速度
    ///</summary>
    public speed: number;

    ///<summary>
    ///子弹的生命周期，单位：秒
    ///</summary>
    public duration: number;

    ///<summary>
    ///子弹已经存在了多久了，单位：秒
    ///毕竟duration是可以被重设的，比如经过一个aoe，生命周期减半了
    ///</summary>
    public timeElapsed: number = 0;

    ///<summary>
    ///子弹的轨迹函数
    ///<param name="t">子弹飞行了多久的时间点，单位秒。</param>
    ///<return>返回这一时间点上的速度和偏移，Vector3就是正常速度正常前进</return>
    ///</summary>
    public tween: BulletTween = null;

    ///<summary>
    ///本帧的移动
    ///</summary>
    private moveForce: Vector3 = new Vector3();

    ///<summary>
    ///本帧的移动信息
    ///</summary>
    public get velocity(): Vector3 {
        return this.moveForce;
    }

    ///<summary>
    ///子弹的移动轨迹是否严格遵循发射出来的角度
    ///</summary>
    public useFireDegreeForever: boolean = false;

    ///<summary>
    ///子弹命中纪录
    ///</summary>
    public hitRecords: BulletHitRecord[] = [];

    ///<summary>
    ///子弹创建后多久是没有碰撞的，这样比如子母弹之类的，不会在创建后立即命中目标，但绝大多子弹还应该是0的
    ///单位：秒
    ///</summary>
    public canHitAfterCreated: number = 0;

    ///<summary>
    ///子弹正在追踪的目标，不太建议使用这个，最好保持null
    ///</summary>
    public followingTarget: GameObject = null;

    ///<summary>
    ///子弹传入的参数，逻辑用的到的临时记录
    ///</summary>
    public param: { [key: string]: any } = {};
    

    ///<summary>
    ///还能命中几次
    ///</summary>
    public hp: number = 1;

    private moveType: MoveType;
    private smoothMove: boolean;

    private unitRotate: UnitRotate;
    private unitMove: UnitMove;
    private viewContainer: GameObject;
    
    private Start(): void {
        // unitRotate = gameObject.GetComponent<UnitRotate>();
        // unitMove = gameObject.GetComponent<UnitMove>();
        this.synchronizedUnits();
    }

    ///<summary>
    ///子弹是否碰到了碰撞
    ///</summary>
    public HitObstacle(): boolean {
        return this.unitMove == null ? false : this.unitMove.hitObstacle;
    }

    ///<summary>
    ///控制子弹移动，这应该是由bulletSystem来调用的
    ///</summary>
    public SetMoveForce(mf: Vector3): void {
        this.moveForce = mf;

        let moveDeg = (
            this.useFireDegreeForever == true ||
            this.timeElapsed <= 0     //还是那个问题，unity的动画走的是update，所以慢了，旋转没转到预设角度，所以我得在第一帧走firedegree
            ) ? this.fireDegree : this.transform.rotation.eulerAngles.y; //欧拉获得的是角度

        this.moveForce.y = 0;
        this.moveForce = Vector3.multiplyByFloat(this.moveForce, this.speed);
        moveDeg += Math.atan2(this.moveForce.x, this.moveForce.z) * 180 / Math.PI;
        
        const mR = moveDeg * Math.PI / 180;
        const mLen = Math.sqrt(Math.pow(this.moveForce.x, 2) + Math.pow(this.moveForce.z, 2));

        this.moveForce.x = Math.sin(mR) * mLen;
        this.moveForce.z = Math.cos(mR) * mLen;

        this.unitMove.MoveBy(this.moveForce);
        this.unitRotate.RotateTo(moveDeg);
    }

    ///<summary>
    ///根据BulletLauncher初始化这个数据
    ///<param name="bullet">bulletLauncher</param>
    ///<param name="targets">子弹允许跟踪的全部目标，在这里根据脚本筛选</param>
    ///</summary>
    public InitByBulletLauncher(bullet: BulletLauncher, targets: GameObject[]): void {
        this.model = bullet.model;
        this.caster = bullet.caster;
        if (this.caster && this.caster.GetComponent<ChaState>()) {
            this.propWhileCast = this.caster.GetComponent<ChaState>().property;
        }
        this.fireDegree = bullet.fireDegree;
        this.speed = bullet.speed;
        this.duration = bullet.duration;
        this.timeElapsed = 0;
        this.tween = bullet.tween;
        this.useFireDegreeForever = bullet.useFireDegreeForever;
        this.canHitAfterCreated = bullet.canHitAfterCreated;
        this.smoothMove = !bullet.model.removeOnObstacle;
        this.moveType = bullet.model.moveType;
        this.hp = bullet.model.hitTimes;

        this.param = {};
        if (bullet.param != null) {
            for (const key in bullet.param) {
                this.param[key] = bullet.param[key];
            }
        }

        this.synchronizedUnits();

        //把视觉特效补充给bulletObj
        if (bullet.model.prefab != "") {
            const bulletEffect = Instantiate<GameObject>(
                Resources.Load<GameObject>("Prefabs/Bullet/" + bullet.model.prefab),
                new Vector3(),
                Quaternion.identity,
                this.viewContainer.transform
            );
            bulletEffect.transform.localPosition = new Vector3(0, this.gameObject.transform.position.y, 0);
            bulletEffect.transform.localRotation = Quaternion.identity;
        }

        this.gameObject.transform.position = new Vector3(
            this.gameObject.transform.position.x,
            0,
            this.gameObject.transform.position.z
        );


        this.followingTarget = bullet.targetFunc == null ? null :
            bullet.targetFunc(this.gameObject, targets);
    }

    //同步一下unitMove和自己的一些状态
    private synchronizedUnits(): void {
        if (!this.unitRotate) this.unitRotate = this.gameObject.GetComponent<UnitRotate>();
        if (!this.unitMove)  this.unitMove = this.gameObject.GetComponent<UnitMove>();
        if (!this.viewContainer) this.viewContainer = this.gameObject.GetComponentInChildren<ViewContainer>().gameObject;

        this.unitMove.smoothMove = this.smoothMove;
        this.unitMove.moveType = this.moveType;
        this.unitMove.bodyRadius = this.model.radius;
    }

    public SetMoveType(toType: MoveType): void {
        this.moveType = toType;
        this.synchronizedUnits();
    }

    ///<summary>
    ///判断子弹是否还能击中某个GameObject
    ///<param name="target">目标gameObject</param>
    ///</summary>
    public CanHit(target: GameObject): boolean {
        if (this.canHitAfterCreated > 0) return false;
        for (let i = 0; i < this.hitRecords.length; i++) {
            if (this.hitRecords[i].target == target) {
                return false;
            }
        }

        const cs = target.GetComponent<ChaState>();
        if (cs && cs.immuneTime > 0) return false;

        return true;
    }

    ///<summary>
    ///添加命中纪录
    ///<param name="target">目标GameObject</param>
    ///</summary>
    public AddHitRecord(target: GameObject): void {
        this.hitRecords.push(new BulletHitRecord(
            target,
            this.model.sameTargetDelay
        ));
    }

    ///<summary>
    ///改变子弹半径
    ///<param name="radius">子弹要改变为的半径，单位米</param>
    ///</summary>
    public SetRadius(radius: number): void {
        this.model.radius = radius;
        this.synchronizedUnits();
    }
}
